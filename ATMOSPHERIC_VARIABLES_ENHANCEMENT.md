# Atmospheric Variables Enhancement for Precipitation Analysis

## Overview

The `src/models/precipitation.py` script has been enhanced to include calculations for atmospheric variables (h500, mslp, and uv850) alongside the existing precipitation statistics for each weather type (WT).

## New Features Added

### 1. Atmospheric Data Loading
- **New parameter**: `atmos_data_dir` in `PrecipitationAnalyzer.__init__()`
- **Automatic detection**: Script automatically detects and loads available atmospheric data files
- **Graceful handling**: If atmospheric data is not available, the script continues with precipitation-only calculations

### 2. New Atmospheric Variables Calculated

For each weather type and each k-value, the script now calculates:

#### Mean Values
- `mean_h500`: Mean 500 hPa geopotential height for each WT
- `mean_mslp`: Mean sea level pressure for each WT  
- `mean_u850`: Mean 850 hPa zonal wind component for each WT
- `mean_v850`: Mean 850 hPa meridional wind component for each WT

#### Anomaly Values
- `anomaly_h500`: Mean anomaly of 500 hPa geopotential height for each WT
- `anomaly_mslp`: Mean anomaly of sea level pressure for each WT
- `anomaly_u850`: Mean anomaly of 850 hPa zonal wind component for each WT
- `anomaly_v850`: Mean anomaly of 850 hPa meridional wind component for each WT

**Note**: Anomalies are calculated by subtracting the overall mean from each day's value, then averaging all anomalies for each weather type.

### 3. Expected Data File Structure

The script expects atmospheric data files in the following format:

```
sample_data/
├── h_500_CONUS_1979_2020.nc          # 500 hPa geopotential height
├── era5_mslp_CONUS_1979_2020.nc      # Mean sea level pressure
├── u_850_CONUS_1979_2020.nc          # 850 hPa zonal wind
└── v_850_CONUS_1979_2020.nc          # 850 hPa meridional wind
```

#### Variable Names and Levels
- **h500**: Variable name `z`, pressure level 500 hPa
- **mslp**: Variable name `msl`, no pressure level selection
- **u850**: Variable name `u`, pressure level 850 hPa  
- **v850**: Variable name `v`, pressure level 850 hPa

## Usage

### Basic Usage (Backward Compatible)
```python
from src.models.precipitation import PrecipitationAnalyzer

# Works exactly as before - will automatically detect atmospheric data
analyzer = PrecipitationAnalyzer(
    precip_path="path/to/precip.nc",
    cluster_path="path/to/clusters.nc",
    output_path="output.nc"
)
analyzer.run()
```

### Specify Custom Atmospheric Data Directory
```python
analyzer = PrecipitationAnalyzer(
    precip_path="path/to/precip.nc",
    cluster_path="path/to/clusters.nc", 
    output_path="output.nc",
    atmos_data_dir="path/to/atmospheric/data"
)
analyzer.run()
```

## Output File Structure

The output NetCDF file now includes additional variables when atmospheric data is available:

### Dimensions
- `k`: Number of clusters (2 to max_k)
- `cluster`: Cluster index (0 to k-1)
- `y`, `x`: Spatial dimensions
- `season`: Seasonal dimension for precipitation variables

### New Variables
All atmospheric variables have dimensions `[k, cluster, y, x]`:

- `mean_h500`: Mean 500 hPa geopotential height (units: m)
- `anomaly_h500`: Mean 500 hPa geopotential height anomaly (units: m)
- `mean_mslp`: Mean sea level pressure (units: Pa)
- `anomaly_mslp`: Mean sea level pressure anomaly (units: Pa)
- `mean_u850`: Mean 850 hPa zonal wind (units: m/s)
- `anomaly_u850`: Mean 850 hPa zonal wind anomaly (units: m/s)
- `mean_v850`: Mean 850 hPa meridional wind (units: m/s)
- `anomaly_v850`: Mean 850 hPa meridional wind anomaly (units: m/s)

## Performance Considerations

- **Memory efficient chunked processing**: Large atmospheric datasets are processed in chunks (default: 1000 time steps) to avoid memory errors
- **Conditional loading**: Only loads and processes atmospheric data if files are available
- **Same time alignment**: Atmospheric data is automatically aligned to match precipitation time series
- **Automatic grid interpolation**: If atmospheric and precipitation data have different spatial resolutions, atmospheric data is automatically interpolated to match the precipitation grid using linear interpolation
- **Optimized for large datasets**: Handles datasets with 14,000+ time steps and 515×599 grids without memory issues

## Grid Resolution Handling

The script automatically handles cases where atmospheric and precipitation data have different spatial resolutions:

- **Automatic detection**: Compares spatial dimensions between datasets
- **Intelligent coordinate matching**: Maps coordinate names (e.g., 'latitude'↔'lat', 'longitude'↔'lon')
- **Linear interpolation**: Uses xarray's interpolation to regrid atmospheric data to precipitation grid
- **Error handling**: Gracefully skips variables that cannot be interpolated

### Example Scenarios Handled:
- Precipitation: 515×599 grid vs Atmospheric: 121×281 grid
- Different coordinate names: 'y','x' vs 'latitude','longitude' vs 'lat','lon'
- Missing atmospheric files: Script continues with precipitation-only calculations

## Memory Management

The script implements chunked processing to handle large atmospheric datasets that would otherwise cause memory errors:

### Problem Solved:
- **Original issue**: Loading 14,600 × 515 × 599 atmospheric arrays (16.8 GiB) caused `MemoryError`
- **Solution**: Process data in chunks of 1000 time steps, accumulating statistics incrementally

### Chunked Processing Features:
- **Configurable chunk size**: Default 1000 time steps (adjustable based on available memory)
- **Incremental statistics**: Computes overall means by accumulating sums and counts across chunks
- **Memory-safe interpolation**: Interpolates each chunk separately before processing
- **Robust error handling**: Continues processing even if individual chunks fail

### Memory Usage:
- **Before**: Required loading entire dataset into memory (~16+ GB for large grids)
- **After**: Uses only chunk-sized memory (~1.2 GB per chunk for 515×599 grids)
- **Scalability**: Can handle arbitrarily large time series with fixed memory footprint

## Testing

Three test scripts have been created to verify the implementation:

### Basic Functionality Test
```bash
python test_atmospheric_variables.py
```
Tests the basic atmospheric variable calculations with matching grid resolutions.

### Grid Mismatch Test
```bash
python test_grid_mismatch_fix.py
```
Tests the grid interpolation functionality with mismatched spatial resolutions (simulates real-world scenario where atmospheric data has different resolution than precipitation data).

### Large Dataset/Memory Test
```bash
python test_chunked_processing.py
```
Tests the chunked processing approach with large synthetic datasets to verify memory efficiency and scalability.

## Backward Compatibility

- **Fully backward compatible**: Existing code will continue to work unchanged
- **Graceful degradation**: If atmospheric data is not available, only precipitation variables are calculated
- **No breaking changes**: All existing functionality remains intact

## Error Handling

- **Missing files**: Script logs warnings for missing atmospheric data files but continues processing
- **Data alignment**: Automatically handles different time series lengths between precipitation and atmospheric data
- **Invalid data**: Uses numpy's NaN handling for missing or invalid atmospheric values

## Future Enhancements

The framework is designed to easily accommodate additional atmospheric variables by:
1. Adding new file paths to `self.atmos_files`
2. Adding variable names to `self.atmos_varnames`  
3. Adding pressure levels to `self.atmos_levels`
4. Adding units/descriptions to the attributes section

This modular approach makes it straightforward to extend the analysis to include other atmospheric variables as needed.
