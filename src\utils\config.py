# -*- coding: utf-8 -*-
"""
Configuration utilities for the weather typing and precipitation regionalization project.
"""
import os
import yaml
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class Config:
    """Configuration manager for the project."""
    
    def __init__(self, config_path=None):
        """
        Initialize the configuration manager.
        
        Parameters:
        -----------
        config_path : str, optional
            Path to the configuration file. If None, uses the default config.
        """
        self.config = {}
        
        if config_path is None:
            # Use default config path
            config_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                'examples',
                'config.yaml'
            )
        
        self.load_config(config_path)
    
    def load_config(self, config_path):
        """
        Load configuration from a YAML file.
        
        Parameters:
        -----------
        config_path : str
            Path to the configuration file
        """
        try:
            with open(config_path, 'r') as f:
                self.config = yaml.safe_load(f)
            logger.info(f"Loaded configuration from {config_path}")
        except Exception as e:
            logger.error(f"Failed to load configuration from {config_path}: {str(e)}")
            raise
    
    def get(self, key, default=None):
        """
        Get a configuration value.
        
        Parameters:
        -----------
        key : str
            Configuration key (can be nested using dot notation, e.g., 'data.latlon_path')
        default : any, optional
            Default value to return if key is not found
            
        Returns:
        --------
        any
            Configuration value
        """
        if '.' in key:
            # Handle nested keys
            parts = key.split('.')
            value = self.config
            for part in parts:
                if isinstance(value, dict) and part in value:
                    value = value[part]
                else:
                    return default
            return value
        else:
            # Simple key
            return self.config.get(key, default)
    
    def set(self, key, value):
        """
        Set a configuration value.
        
        Parameters:
        -----------
        key : str
            Configuration key (can be nested using dot notation, e.g., 'data.latlon_path')
        value : any
            Value to set
        """
        if '.' in key:
            # Handle nested keys
            parts = key.split('.')
            config = self.config
            for part in parts[:-1]:
                if part not in config:
                    config[part] = {}
                config = config[part]
            config[parts[-1]] = value
        else:
            # Simple key
            self.config[key] = value
    
    def save(self, config_path=None):
        """
        Save configuration to a YAML file.
        
        Parameters:
        -----------
        config_path : str, optional
            Path to save the configuration file. If None, uses the default config path.
        """
        if config_path is None:
            config_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                'examples',
                'config.yaml'
            )
        
        try:
            with open(config_path, 'w') as f:
                yaml.dump(self.config, f, default_flow_style=False)
            logger.info(f"Saved configuration to {config_path}")
        except Exception as e:
            logger.error(f"Failed to save configuration to {config_path}: {str(e)}")
            raise
