# Weather Typing and Precipitation Regionalization

## Project Description
This project implements weather typing and precipitation regionalization analysis for climate studies. It uses clustering techniques to identify weather patterns and precipitation regions across the Continental United States (CONUS), and evaluates the homogeneity of these regions using L-moment statistics.

## Prerequisites

### Required Data Files
Ensure you have the following data files in your `sample_data/` directory:

1. **Precipitation Data**: `PRISM_daily_CCSM_interpolated_n5_1981_2020.nc`
2. **Annual Maximum Series**: `PRISM_1981_2020_AMS.nc`
3. **Coordinate Data**: `WRF_CCSM_lat_lon.nc`
4. **Atmospheric Variables** (for weather typing):
   - `h500_CONUS_1979_2020.nc` (500 hPa geopotential height)
   - `mslp_CONUS_1979_2020.nc` (mean sea level pressure)
   - `u850_CONUS_1979_2020.nc` (850 hPa zonal wind)
   - `v850_CONUS_1979_2020.nc` (850 hPa meridional wind)

### Python Environment
Install required packages:
```bash
pip install numpy pandas xarray netcdf4 matplotlib cartopy scikit-learn scipy
```

## Quick Start

### **Complete Workflow (Recommended)**
Run the full analysis pipeline:

```bash
# Step 1: Weather Typing Analysis
python -m src.models.run_wt_clustering \
  --outdir ./run_w35 \
  --maxclust 35 \
  --nsim 100

# Step 2: Generate Precipitation Statistics for Weather Types
python -m src.models.make_WT_precipfiles \
  --precip-path sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc \
  --cluster-path run_w35/CI_results.nc \
  --output-path cluster_precip_stats.nc \
  --atmos-data-dir sample_data

# Step 3: Precipitation Regionalization (without Weather Types)
python -m src.models.run_precip_regions \
  --outdir ./regionalization_wo_WT \
  --maxclust 40 \
  --nsim 100

# Step 4: Precipitation Regionalization with Weather Types
python -m src.models.run_precipitation_regions_w_WTs \
  --wt-range 2-35 \
  --maxclust 40 \
  --nsim 100 \
  --cluster-stats cluster_precip_stats.nc

# Step 5: Heterogeneity Analysis
python -m src.models.run_heterogeneity_workflow \
  --precip-path sample_data/PRISM_1981_2020_AMS.nc \
  --base-dir ./ \
  --output-base-dir heterogeneity_results \
  --max-clusters 40 \
  --nsim 500

# Step 6: Compare Results
python -m src.models.compare_heterogeneity_results \
  --results-dir heterogeneity_results \
  --output-dir heterogeneity_comparison
```

## Workflow Overview

The analysis workflow consists of six main steps:

```
┌─────────────────────┐     ┌─────────────────────┐     ┌─────────────────────┐     ┌─────────────────────┐
│                     │     │                     │     │                     │     │                     │
│   Weather Typing    │────▶│    Precipitation    │────▶│    Precipitation    │────▶│    Precipitation    │
│      Analysis       │     │     Statistics      │     │   Regionalization   │     │   Regionalization   │
│                     │     │                     │     │                     │     │   with Weather Types│
└─────────────────────┘     └─────────────────────┘     └─────────────────────┘     └─────────────────────┘
                                                                │                              │
                                                                │                              │
                                                                ▼                              ▼
                                                        ┌─────────────────────┐     ┌─────────────────────┐
                                                        │                     │     │                     │
                                                        │    Heterogeneity    │     │    Heterogeneity    │
                                                        │      Analysis       │     │      Analysis       │
                                                        │                     │     │                     │
                                                        └─────────────────────┘     └─────────────────────┘
                                                                │                              │
                                                                │                              │
                                                                └──────────────┬───────────────┘
                                                                               │
                                                                               ▼
                                                                      ┌─────────────────────┐
                                                                      │                     │
                                                                      │    Heterogeneity    │
                                                                      │     Comparison      │
                                                                      │                     │
                                                                      └─────────────────────┘
```

1. **Weather Typing Analysis**: Identifies distinct weather patterns (weather types) using atmospheric variables.
2. **Precipitation Statistics**: Calculates precipitation statistics for each weather type, including atmospheric variables.
3. **Precipitation Regionalization**: Identifies homogeneous precipitation regions without considering weather types.
4. **Precipitation Regionalization with Weather Types**: Identifies homogeneous precipitation regions considering weather type information.
5. **Heterogeneity Analysis**: Evaluates the homogeneity of precipitation regions using L-moment statistics (H1, H2, H3) as described in Hosking and Wallis (1997).
6. **Heterogeneity Comparison**: Compares heterogeneity results across different regionalization methods to identify optimal cluster configurations.
## Detailed Step-by-Step Instructions

### Step 1: Weather Typing Analysis

**Purpose**: Identify distinct weather patterns using atmospheric variables.

**Command**:
```bash
python -m src.models.run_wt_clustering \
  --outdir ./run_w35 \
  --maxclust 35 \
  --nsim 100
```

**Parameters**:
- `--outdir`: Output directory for weather typing results
- `--maxclust`: Maximum number of weather types to test (typically 35)
- `--nsim`: Number of simulations for classifiability index calculation

**Inputs**: Atmospheric variables from `sample_data/` (h500, mslp, u850, v850)
**Outputs**:
- `run_w35/CI_results.nc`: Weather type assignments and classifiability indices
- `run_w35/plot_ci.png`: Classifiability index plot

### Step 2: Generate Precipitation Statistics for Weather Types

**Purpose**: Calculate precipitation statistics for each weather type, including atmospheric variables.

**Command**:
```bash
python -m src.models.make_WT_precipfiles \
  --precip-path sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc \
  --cluster-path run_w35/CI_results.nc \
  --output-path cluster_precip_stats.nc \
  --atmos-data-dir sample_data
```

**Parameters**:
- `--precip-path`: Path to daily precipitation data
- `--cluster-path`: Path to weather type assignments from Step 1
- `--output-path`: Output file for precipitation statistics
- `--atmos-data-dir`: Directory containing atmospheric variable files

**Key Features**:
- **Memory-optimized**: Uses chunked processing for large datasets
- **Grid interpolation**: Automatically handles different spatial resolutions
- **Atmospheric variables**: Calculates means and anomalies for h500, mslp, u850, v850
- **New variable**: `mean_annual_precip` - mean over all years of yearly mean precipitation

**Outputs**:
- `cluster_precip_stats.nc`: Comprehensive precipitation and atmospheric statistics

### Step 3: Precipitation Regionalization (without Weather Types)

**Purpose**: Identify homogeneous precipitation regions based on precipitation characteristics alone.

**K-means Clustering (Default)**:
```bash
python -m src.models.run_precip_regions \
  --outdir ./regionalization_wo_WT \
  --maxclust 40 \
  --nsim 100
```

**Hierarchical Clustering**:
```bash
python -m src.models.run_precip_regions \
  --outdir ./regionalization_wo_WT \
  --maxclust 40 \
  --nsim 100 \
  --method hierarchical
```

**Parameters**:
- `--outdir`: Output directory for regionalization results
- `--maxclust`: Maximum number of regions to test
- `--nsim`: Number of simulations for classifiability index
- `--method`: Clustering method (`kmeans` or `hierarchical`)

**Outputs**:
- `regionalization_wo_WT/CI_results.nc`: Region assignments
- `regionalization_wo_WT/plot_ci.png`: Classifiability index plot
- `regionalization_wo_WT/plots/`: Regional visualizations
### Step 4: Precipitation Regionalization with Weather Types

**Purpose**: Identify homogeneous precipitation regions considering weather type information.

**K-means Clustering (Default)**:
```bash
python -m src.models.run_precipitation_regions_w_WTs \
  --wt-range 2-35 \
  --maxclust 40 \
  --nsim 100 \
  --cluster-stats cluster_precip_stats.nc
```

**Hierarchical Clustering**:
```bash
python -m src.models.run_precipitation_regions_w_WTs \
  --wt-range 2-35 \
  --maxclust 40 \
  --nsim 100 \
  --cluster-stats cluster_precip_stats.nc \
  --method hierarchical
```

**Parameters**:
- `--wt-range`: Range of weather types to process (e.g., 2-35)
- `--maxclust`: Maximum number of regions to test
- `--nsim`: Number of simulations for classifiability index
- `--cluster-stats`: Precipitation statistics file from Step 2
- `--method`: Clustering method (`kmeans` or `hierarchical`)

**Outputs**:
- `regionalization_w_WT_*_kmeans/` or `regionalization_w_WT_*_hierarchical/`: Separate directories for each weather type count
- Each directory contains region assignments, CI plots, and visualizations

### Step 5: Heterogeneity Analysis

**Purpose**: Evaluate the homogeneity of precipitation regions using L-moment statistics.

**Run on All Regionalization Results**:
```bash
python -m src.models.run_heterogeneity_workflow \
  --precip-path sample_data/PRISM_1981_2020_AMS.nc \
  --base-dir ./ \
  --output-base-dir heterogeneity_results \
  --max-clusters 40 \
  --nsim 500
```

**Run on Single Regionalization Result**:
```bash
python -m src.models.run_heterogeneity_analysis \
  --precip-path sample_data/PRISM_1981_2020_AMS.nc \
  --cluster-path regionalization_wo_WT/CI_results.nc \
  --output-dir heterogeneity_results/regionalization_wo_WT \
  --max-clusters 40 \
  --nsim 500
```

**Parameters**:
- `--precip-path`: Path to annual maximum series data
- `--base-dir`: Base directory to search for regionalization results
- `--output-base-dir`: Output directory for heterogeneity results
- `--max-clusters`: Maximum number of clusters to analyze
- `--nsim`: Number of simulations for heterogeneity statistics

**Outputs**:
- Heterogeneity statistics (H1, H2, H3) for each regionalization method
- Summary tables and visualizations

### Step 6: Compare Heterogeneity Results

**Purpose**: Compare heterogeneity statistics across different regionalization methods.

**Command**:
```bash
python -m src.models.compare_heterogeneity_results \
  --results-dir heterogeneity_results \
  --output-dir heterogeneity_comparison
```

**Parameters**:
- `--results-dir`: Directory containing heterogeneity results from Step 5
- `--output-dir`: Output directory for comparison results

**Outputs**:
- Combined summary tables across all methods
- Comparison visualizations
- Recommendations for optimal cluster configurations
## Clustering Methods

This project supports two clustering methods for precipitation regionalization:

### **K-means Clustering (Default)**
- **Algorithm**: K-means with multiple random initializations
- **Characteristics**:
  - Fast computation for large datasets
  - Prefers spherical, compact clusters
  - Good for well-separated, similar-sized clusters
- **Best for**: Traditional regionalization approaches

### **Hierarchical Clustering**
- **Algorithm**: Ward's agglomerative hierarchical clustering
- **Characteristics**:
  - Deterministic results (no random initialization)
  - Handles arbitrary cluster shapes
  - Reveals natural hierarchy in data
  - Generates dendrogram for visualization
- **Best for**: Exploring nested relationships and irregular cluster shapes

### **Method Selection**
Use the `--method` parameter to choose your clustering approach:
- `--method kmeans` (default)
- `--method hierarchical`

Both methods use the same classifiability index (CI) calculation for fair comparison.

## Weather Typing Precipitation Visualization

Create spatial maps of weather typing precipitation data:

```bash
# Create spatial maps for weather typing variables
python -m src.models.plot_wt_precipitation \
  --data-path cluster_precip_stats.nc \
  --latlon-path sample_data/WRF_CCSM_lat_lon.nc \
  --output-dir wt_maps \
  --variables mean_annual_max wt_freq_gridpoint wt_freq_ams_gridpoint mean_annual_precip
```

**Performance optimized for large datasets:**
```bash
python -m src.models.plot_wt_precipitation \
  --data-path cluster_precip_stats.nc \
  --latlon-path sample_data/WRF_CCSM_lat_lon.nc \
  --output-dir wt_maps \
  --max-k 10 \
  --sample-k 5 \
  --debug
```

This creates 2D spatial maps showing:
- Mean annual maximum precipitation by weather type
- **Mean annual precipitation by weather type** (NEW)
- Weather type frequency at each gridpoint
- Weather type frequency during annual maximum events
- Atmospheric variable means and anomalies (h500, mslp, u850, v850)
- Separate maps for each k value and cluster
- **Coordinates loaded from separate lat/lon file for proper geographic display**
## Expected Outputs

The workflow produces the following outputs:

### 1. Weather Typing Analysis
- `run_w35/CI_results.nc`: Weather type assignments and classifiability indices
- `run_w35/plot_ci.png`: Plot of classifiability indices
- `run_w35/plot_CIci.png`: Plot with confidence intervals

### 2. Precipitation Statistics
- `cluster_precip_stats.nc`: Comprehensive precipitation and atmospheric statistics including:
  - Precipitation statistics for each weather type
  - **Atmospheric variable means and anomalies** (h500, mslp, u850, v850)
  - **Mean annual precipitation** (`mean_annual_precip`)
  - Weather type frequencies
  - Seasonal statistics

### 3. Precipitation Regionalization (without Weather Types)
- `regionalization_wo_WT/CI_results.nc`: Region assignments
- `regionalization_wo_WT/plot_ci.png`: Classifiability index plot
- `regionalization_wo_WT/plots/`: Regional visualizations

### 4. Precipitation Regionalization with Weather Types
- `regionalization_w_WT_*/CI_results.nc`: Region assignments for each weather type count
- `regionalization_w_WT_*/plot_ci.png`: CI plots
- `regionalization_w_WT_*/plots/`: Regional visualizations

### 5. Heterogeneity Analysis
- `heterogeneity_results/*/heterogeneity_summary.csv`: Summary of heterogeneity statistics
- `heterogeneity_results/*/heterogeneity_overall_summary.csv`: Overall summary across all k values
- `heterogeneity_results/*/heterogeneity_k*.csv`: Detailed statistics for each k value
- `heterogeneity_results/*/heterogeneity_statistics.nc`: NetCDF file with all statistics
- `heterogeneity_results/*/heterogeneity_statistics.png`: Plot of heterogeneity statistics
- `heterogeneity_results/*/heterogeneity_heatmap.png`: Heatmap of heterogeneity values

### 6. Heterogeneity Comparison
- `heterogeneity_comparison/combined_heterogeneity_summary.csv`: Combined summary across all methods
- `heterogeneity_comparison/combined_overall_summary.csv`: Combined overall summary
- `heterogeneity_comparison/best_k_summary.csv`: Summary of best k values for each method
- `heterogeneity_comparison/top_k_detailed.csv`: Detailed statistics for top k values
- `heterogeneity_comparison/H1_comparison.png`: Comparison of H1 statistics across methods
- `heterogeneity_comparison/pct_acceptable_comparison.png`: Comparison of percentage of acceptable regions
- `heterogeneity_comparison/homogeneity_categories.png`: Breakdown of regions by homogeneity category

### 7. Weather Typing Visualization
- `wt_maps/`: Spatial maps of weather typing variables
  - Mean annual maximum precipitation maps
  - **Mean annual precipitation maps** (NEW)
  - Weather type frequency maps
  - **Atmospheric variable maps** (h500, mslp, u850, v850)
  - Separate maps for each k value and cluster

## Performance Considerations

### Memory Management
- **Chunked Processing**: The precipitation statistics generation uses chunked processing to handle large datasets efficiently (processes data in 1000-timestep chunks)
- **Grid Interpolation**: Automatic interpolation handles different spatial resolutions between datasets
- **Memory Monitoring**: Use `--debug` flag to monitor memory usage during processing
- **Memory Requirements**: Typical memory usage is ~1.2 GB per chunk for large datasets

### Computational Performance
- **K-means**: Fast for large datasets, suitable for initial exploration
- **Hierarchical**: More computationally intensive but provides deterministic results
- **Parallel Processing**: Some operations use multiple cores when available
- **Atmospheric Variables**: Processing includes mean and anomaly calculations for h500, mslp, u850, v850

### Recommended Settings
- **Small datasets** (< 1GB): Use default settings
- **Large datasets** (> 5GB): Consider reducing `--nsim` to 50-100 for faster processing
- **Very large datasets** (> 10GB): Chunked processing is automatically enabled

### Key Performance Features
- **Automatic grid interpolation** for spatial resolution mismatches
- **Memory-efficient chunked processing** for large time series
- **Optimized atmospheric variable calculations** with anomaly processing
- **Efficient weather type frequency calculations**

## Troubleshooting

### Common Issues

1. **Memory Errors**:
   - Chunked processing is automatically enabled for large datasets
   - Reduce the number of simulations (`--nsim`) if needed
   - Close other applications to free up memory

2. **Spatial Resolution Mismatch**:
   - The system automatically handles different grid resolutions through interpolation
   - Ensure coordinate variables are properly named (lat/latitude, lon/longitude, y, x)
   - Grid interpolation works between different coordinate systems

3. **Missing Data Files**:
   - Verify all required data files are in the `sample_data/` directory
   - Check atmospheric variable files (h500, mslp, u850, v850) are present
   - Ensure coordinate file `WRF_CCSM_lat_lon.nc` is available

4. **Slow Performance**:
   - Use K-means clustering for faster results
   - Reduce the maximum number of clusters (`--maxclust`)
   - Use fewer simulations (`--nsim`)
   - Chunked processing automatically optimizes memory usage

### Getting Help

If you encounter issues:
1. Check the error messages for specific guidance
2. Use the `--debug` flag for detailed logging
3. Verify your data files match the expected format
4. Ensure you have sufficient memory and disk space
5. Check that atmospheric variable files have proper time alignment

## Interpreting Heterogeneity Results

The heterogeneity statistics (H1, H2, H3) are interpreted according to Hosking and Wallis (1997):

- **H < 1**: The region is "acceptably homogeneous"
- **1 ≤ H < 2**: The region is "possibly heterogeneous"
- **H ≥ 2**: The region is "definitely heterogeneous"

H1 is based on L-CV, H2 on L-CV and L-skewness, and H3 on L-skewness and L-kurtosis. H1 is typically given the most weight in the analysis.

## Choosing Between Clustering Methods

### **When to Use K-means**
- **Large datasets**: Faster computation for big data
- **Spherical clusters expected**: When regions are roughly circular/compact
- **Established workflows**: When comparing with previous K-means results
- **Speed priority**: When computational efficiency is important

### **When to Use Hierarchical Clustering**
- **Exploring data structure**: When you want to understand natural groupings
- **Irregular cluster shapes**: When regions may have complex boundaries
- **Deterministic results**: When you need reproducible results without random initialization
- **Hierarchy visualization**: When dendrogram insights are valuable
- **Small to medium datasets**: When computational cost is not a major concern

### **Method Comparison Workflow**
1. **Run both methods** on a subset of your data (e.g., `--wt-range 2-10`)
2. **Compare CI values** to see which method produces better cluster separation
3. **Examine dendrograms** (hierarchical) for natural cluster numbers
4. **Evaluate heterogeneity results** to see which produces more homogeneous regions
5. **Choose the optimal method** based on your specific research goals

### **Performance Considerations**
- **K-means**: O(n×k×i×d) where n=samples, k=clusters, i=iterations, d=dimensions
- **Hierarchical**: O(n³) for full hierarchy, but uses efficient Ward linkage
- **Recommendation**: Test both methods on your data to determine the best approach

## References

- Hosking, J.R.M. and Wallis, J.R. (1997). Regional Frequency Analysis: An Approach Based on L-Moments. Cambridge University Press.
- Ward, J.H. (1963). Hierarchical grouping to optimize an objective function. Journal of the American Statistical Association, 58(301), 236-244.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
````