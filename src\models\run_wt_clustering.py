#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script to run weather typing clustering analysis.
"""
import os
import logging
import argparse
from pathlib import Path
from src.features.data_preprocessing import AtmosClusterer
from src.models.clustering import EOFClusteringAnalyzer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Run weather typing clustering analysis')
    parser.add_argument('--outdir', type=str, default='./CONUS_ERA5',
                        help='Output directory for results')
    parser.add_argument('--maxclust', type=int, default=35,
                        help='Maximum number of clusters to consider')
    parser.add_argument('--nsim', type=int, default=100,
                        help='Number of simulations')
    parser.add_argument('--retain-variance', type=float, default=0.85,
                        help='Fraction of variance to retain in EOF analysis')
    return parser.parse_args()

def main():
    """Main function to run weather typing clustering."""
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.outdir, exist_ok=True)
    
    # Define paths and parameters
    latlon_path = "sample_data/mslp_CONUS_1979_2020.nc"
    variable_paths = {
        "h500": "sample_data/h_500_CONUS_1979_2020.nc",
        "mslp": "sample_data/era5_mslp_CONUS_1979_2020.nc",
        "u": "sample_data/u_850_CONUS_1979_2020.nc",
        "v": "sample_data/v_850_CONUS_1979_2020.nc",
    }
    variable_names = {
        "h500": "z",
        "mslp": "msl",
        "u": "u",
        "v": "v",
    }
    levels = {
        "h500": 500,
        "mslp": None,
        "u": 850,
        "v": 850,
    }
    
    # Initialize and run atmospheric clusterer
    logger.info("Initializing atmospheric clusterer")
    clusterer = AtmosClusterer(
        latlon_path=latlon_path,
        variable_paths=variable_paths,
        variable_names=variable_names,
        levels=levels,
        retain_variance=args.retain_variance
    )
    
    # Run dimensionality reduction
    logger.info("Running dimensionality reduction")
    reduced_data = clusterer.run(plot_filename=os.path.join(args.outdir, "eof_variance_plot.png"))
    logger.info(f"Reduced data shape: {reduced_data.shape}")
    
    # Initialize and run clustering analyzer
    logger.info("Initializing clustering analyzer")
    analyzer = EOFClusteringAnalyzer(
        reduced_data, 
        outdir=args.outdir, 
        maxclust=args.maxclust, 
        nsim=args.nsim
    )
    
    # Compute or load classifiability index
    logger.info("Computing classifiability index")
    analyzer.load_or_compute_ci()
    analyzer.plot_ci()
    
    # Optional: Generate red noise and test CI with memory optimization
    if args.nsim > 0:
        logger.info("Running memory-efficient red noise analysis")
        analyzer.run_memory_efficient(
            reduced_nsim=min(20, args.nsim),  # Use at most 20 simulations
            max_k_test=min(15, args.maxclust),  # Test up to 15 clusters
            chunk_size=10  # Process in chunks of 10
        )
    
    logger.info("Weather typing clustering complete")

if __name__ == '__main__':
    main()
