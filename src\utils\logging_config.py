# -*- coding: utf-8 -*-
"""
Logging configuration for the weather typing and precipitation regionalization project.
"""
import os
import logging
import logging.handlers
from datetime import datetime

def setup_logging(log_level=logging.INFO, log_dir='logs', log_to_console=True, log_to_file=True):
    """
    Set up logging configuration.
    
    Parameters:
    -----------
    log_level : int, optional
        Logging level (default: logging.INFO)
    log_dir : str, optional
        Directory to store log files (default: 'logs')
    log_to_console : bool, optional
        Whether to log to console (default: True)
    log_to_file : bool, optional
        Whether to log to file (default: True)
    
    Returns:
    --------
    logging.Logger
        Configured logger
    """
    # Create logger
    logger = logging.getLogger()
    logger.setLevel(log_level)
    
    # Clear existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Create formatters
    console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s')
    
    # Console handler
    if log_to_console:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
    
    # File handler
    if log_to_file:
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = os.path.join(log_dir, f'weather_typing_{timestamp}.log')
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    return logger

def get_logger(name):
    """
    Get a logger with the specified name.
    
    Parameters:
    -----------
    name : str
        Logger name
    
    Returns:
    --------
    logging.Logger
        Logger instance
    """
    return logging.getLogger(name)
