#!/usr/bin/env python3
"""
Test PE3 fitting with the book's T4 value: τ₄ = 0.1366
"""

import numpy as np
from lmoments3 import distr
import subprocess
import os

def test_pe3_with_t4():
    """Test PE3 with book's T4 value"""
    
    print("TESTING PE3 WITH BOOK'S T4 VALUE")
    print("=" * 50)
    
    # Book's exact values including T4
    lambda1_R = 1.0
    tau2_R = 0.1103
    tau3_R = 0.0279
    tau4_R = 0.1366  # New value from the book
    
    print(f"Book's complete L-moment ratios:")
    print(f"  λ₁ᴿ = {lambda1_R:.4f}")
    print(f"  τ₂ᴿ = {tau2_R:.4f}")
    print(f"  τ₃ᴿ = {tau3_R:.4f}")
    print(f"  τ₄ᴿ = {tau4_R:.4f}")
    print(f"  Expected γ = 0.1626")
    
    # Our calculated values for comparison
    our_tau2 = 0.110298
    our_tau3 = 0.027859
    
    print(f"\nOur calculated values:")
    print(f"  λ₁ᴿ = {lambda1_R:.6f}")
    print(f"  τ₂ᴿ = {our_tau2:.6f}")
    print(f"  τ₃ᴿ = {our_tau3:.6f}")
    print(f"  τ₄ᴿ = 0.0000 (not calculated)")
    
    print(f"\n{'='*60}")
    print("PE3 FITTING TESTS")
    print(f"{'='*60}")
    
    # Test 1: Our values with T4=0
    print(f"\nTest 1: Our calculated values (T4=0)")
    try:
        lmoms_our = [lambda1_R, our_tau2, our_tau3, 0.0]
        pe3_our = distr.pe3.lmom_fit(lmom_ratios=lmoms_our)
        
        print(f"  Parameters:")
        print(f"    μ (loc): {pe3_our['loc']:.6f}")
        print(f"    σ (scale): {pe3_our['scale']:.6f}")
        print(f"    γ (skew): {pe3_our['skew']:.6f}")
        print(f"  Difference from book γ: {abs(pe3_our['skew'] - 0.1626):.6f}")
        
    except Exception as e:
        print(f"  Error: {e}")
    
    # Test 2: Book values with T4=0
    print(f"\nTest 2: Book values with T4=0")
    try:
        lmoms_book_t4_0 = [lambda1_R, tau2_R, tau3_R, 0.0]
        pe3_book_t4_0 = distr.pe3.lmom_fit(lmom_ratios=lmoms_book_t4_0)
        
        print(f"  Parameters:")
        print(f"    μ (loc): {pe3_book_t4_0['loc']:.6f}")
        print(f"    σ (scale): {pe3_book_t4_0['scale']:.6f}")
        print(f"    γ (skew): {pe3_book_t4_0['skew']:.6f}")
        print(f"  Difference from book γ: {abs(pe3_book_t4_0['skew'] - 0.1626):.6f}")
        
    except Exception as e:
        print(f"  Error: {e}")
    
    # Test 3: Book values with T4=0.1366
    print(f"\nTest 3: Book values with T4={tau4_R}")
    try:
        lmoms_book_full = [lambda1_R, tau2_R, tau3_R, tau4_R]
        pe3_book_full = distr.pe3.lmom_fit(lmom_ratios=lmoms_book_full)
        
        print(f"  Parameters:")
        print(f"    μ (loc): {pe3_book_full['loc']:.6f}")
        print(f"    σ (scale): {pe3_book_full['scale']:.6f}")
        print(f"    γ (skew): {pe3_book_full['skew']:.6f}")
        print(f"  Difference from book γ: {abs(pe3_book_full['skew'] - 0.1626):.6f}")
        
        if abs(pe3_book_full['skew'] - 0.1626) < 0.001:
            print(f"  *** EXCELLENT MATCH! ***")
        elif abs(pe3_book_full['skew'] - 0.1626) < 0.01:
            print(f"  *** GOOD MATCH! ***")
        else:
            print(f"  Still doesn't match exactly")
        
        # Verify the fit by checking theoretical L-moments
        print(f"\n  Verification (theoretical L-moments from fitted parameters):")
        theo_lmoms = distr.pe3.lmom_ratios(**pe3_book_full)
        print(f"    λ₁: {theo_lmoms[0]:.6f} (target: {lambda1_R:.4f})")
        print(f"    τ₂: {theo_lmoms[1]:.6f} (target: {tau2_R:.4f})")
        print(f"    τ₃: {theo_lmoms[2]:.6f} (target: {tau3_R:.4f})")
        print(f"    τ₄: {theo_lmoms[3]:.6f} (target: {tau4_R:.4f})")
        
        # Check fit quality
        errors = [
            abs(theo_lmoms[0] - lambda1_R),
            abs(theo_lmoms[1] - tau2_R),
            abs(theo_lmoms[2] - tau3_R),
            abs(theo_lmoms[3] - tau4_R)
        ]
        
        print(f"\n  Fit errors:")
        for i, error in enumerate(errors):
            print(f"    τ{i+1} error: {error:.8f}")
        
        if all(error < 1e-6 for error in errors):
            print(f"    ✓ Perfect fit to all L-moment ratios!")
        elif all(error < 1e-3 for error in errors[:3]):
            print(f"    ✓ Good fit to first 3 L-moment ratios")
        
    except Exception as e:
        print(f"  Error: {e}")
    
    # Test with R lmomco
    print(f"\n{'='*60}")
    print("R lmomco WITH BOOK'S T4 VALUE")
    print(f"{'='*60}")
    
    r_script = f'''
library(lmomco)

# Book's complete values including T4
lambda1_R <- 1.0
tau2_R <- 0.1103
tau3_R <- 0.0279
tau4_R <- 0.1366

cat("R lmomco with book's complete L-moment ratios\\n")
cat("==============================================\\n")
cat("λ₁ᴿ =", lambda1_R, "\\n")
cat("τ₂ᴿ =", tau2_R, "\\n")
cat("τ₃ᴿ =", tau3_R, "\\n")
cat("τ₄ᴿ =", tau4_R, "\\n")
cat("Expected γ = 0.1626\\n\\n")

# Test 1: With T4 = 0
cat("Test 1: PE3 with T4 = 0\\n")
cat("========================\\n")
lmoms_t4_0 <- vec2lmom(c(lambda1_R, tau2_R, tau3_R, 0.0))
pe3_t4_0 <- lmom2par(lmoms_t4_0, type="pe3")

if (!is.null(pe3_t4_0) && !is.null(pe3_t4_0$para)) {{
  cat("Parameters:\\n")
  cat("  μ:", sprintf("%.6f", pe3_t4_0$para[1]), "\\n")
  cat("  σ:", sprintf("%.6f", pe3_t4_0$para[2]), "\\n")
  cat("  γ:", sprintf("%.6f", pe3_t4_0$para[3]), "\\n")
  cat("Difference from book γ:", sprintf("%.6f", abs(pe3_t4_0$para[3] - 0.1626)), "\\n\\n")
}}

# Test 2: With T4 = 0.1366
cat("Test 2: PE3 with T4 = 0.1366\\n")
cat("=============================\\n")
lmoms_full <- vec2lmom(c(lambda1_R, tau2_R, tau3_R, tau4_R))
pe3_full <- lmom2par(lmoms_full, type="pe3")

if (!is.null(pe3_full) && !is.null(pe3_full$para)) {{
  cat("Parameters:\\n")
  cat("  μ:", sprintf("%.6f", pe3_full$para[1]), "\\n")
  cat("  σ:", sprintf("%.6f", pe3_full$para[2]), "\\n")
  cat("  γ:", sprintf("%.6f", pe3_full$para[3]), "\\n")
  
  gamma_diff <- abs(pe3_full$para[3] - 0.1626)
  cat("Difference from book γ:", sprintf("%.6f", gamma_diff), "\\n")
  
  if (gamma_diff < 0.001) {{
    cat("EXCELLENT MATCH!\\n")
  }} else if (gamma_diff < 0.01) {{
    cat("GOOD MATCH!\\n")
  }}
  
  # Verify fit
  cat("\\nVerification (theoretical L-moments):\\n")
  theo_lmoms <- par2lmom(pe3_full)
  if (!is.null(theo_lmoms)) {{
    cat("  λ₁:", sprintf("%.6f", theo_lmoms$lambdas[1]), "(target:", sprintf("%.4f", lambda1_R), ")\\n")
    cat("  τ₂:", sprintf("%.6f", theo_lmoms$ratios[2]), "(target:", sprintf("%.4f", tau2_R), ")\\n")
    cat("  τ₃:", sprintf("%.6f", theo_lmoms$ratios[3]), "(target:", sprintf("%.4f", tau3_R), ")\\n")
    cat("  τ₄:", sprintf("%.6f", theo_lmoms$ratios[4]), "(target:", sprintf("%.4f", tau4_R), ")\\n")
  }}
}} else {{
  cat("Error fitting PE3 with T4\\n")
}}
'''
    
    # Write and run R script
    try:
        with open('test_pe3_t4.R', 'w', encoding='utf-8') as f:
            f.write(r_script)
        
        # Try to run R script
        rscript_paths = [
            'Rscript',
            r'C:\Program Files\R\R-4.3.0\bin\x64\Rscript.exe',
            r'C:\Program Files\R\R-4.3.0\bin\Rscript.exe'
        ]
        
        result = None
        for rscript_path in rscript_paths:
            try:
                result = subprocess.run(
                    [rscript_path, 'test_pe3_t4.R'], 
                    capture_output=True, 
                    text=True,
                    cwd=os.getcwd()
                )
                break
            except FileNotFoundError:
                continue
        
        if result and result.returncode == 0:
            print(result.stdout)
            if result.stderr:
                print("Warnings:")
                print(result.stderr)
        else:
            print("Could not run R script")
            
    except Exception as e:
        print(f"Error with R script: {e}")
    
    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    
    print("Key findings:")
    print("• Book provides complete L-moment ratios including τ₄ = 0.1366")
    print("• PE3 is a 3-parameter distribution, so τ₄ should theoretically be determined by the first 3 moments")
    print("• Including τ₄ in the fitting may help resolve the gamma discrepancy")
    print("• Both Python lmoments3 and R lmomco should handle 4-moment fitting")

if __name__ == "__main__":
    test_pe3_with_t4()
