# -*- coding: utf-8 -*-
"""
<PERSON><PERSON><PERSON> to prepare data for analysis.
"""
import os
import argparse
import logging
from pathlib import Path
from src.utils.logging_config import setup_logging
from src.utils.data_validator import DataValidator

logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Prepare data for analysis')
    parser.add_argument('--input-dir', type=str, default='./sample_data',
                        help='Directory containing input data files')
    parser.add_argument('--output-dir', type=str, default='./data/processed',
                        help='Directory for output files')
    parser.add_argument('--validate', action='store_true',
                        help='Validate input data files')
    return parser.parse_args()

def validate_input_files(input_dir):
    """
    Validate input data files.
    
    Parameters:
    -----------
    input_dir : str
        Directory containing input data files
        
    Returns:
    --------
    bool
        True if all files are valid, False otherwise
    """
    logger.info("Validating input data files...")
    
    validator = DataValidator()
    
    # Define required files and their validation criteria
    validation_tasks = [
        {
            'file': os.path.join(input_dir, 'mslp_CONUS_1979_2020.nc'),
            'required_vars': ['msl'],
            'required_dims': ['time']
        },
        {
            'file': os.path.join(input_dir, 'h_500_CONUS_1979_2020.nc'),
            'required_vars': ['z'],
            'required_dims': ['time', 'pressure_level']
        },
        {
            'file': os.path.join(input_dir, 'u_850_CONUS_1979_2020.nc'),
            'required_vars': ['u'],
            'required_dims': ['time', 'pressure_level']
        },
        {
            'file': os.path.join(input_dir, 'v_850_CONUS_1979_2020.nc'),
            'required_vars': ['v'],
            'required_dims': ['time', 'pressure_level']
        },
        {
            'file': os.path.join(input_dir, 'PRISM_daily_CCSM_interpolated_n5_1981_2020.nc'),
            'required_vars': ['precip'],
            'required_dims': ['time']
        },
        {
            'file': os.path.join(input_dir, 'WRF_CCSM_lat_lon.nc'),
            'required_vars': ['latitude', 'longitude'],
            'required_dims': []
        }
    ]
    
    all_valid = True
    for task in validation_tasks:
        valid = validator.validate_netcdf_file(
            task['file'], 
            required_vars=task.get('required_vars'), 
            required_dims=task.get('required_dims')
        )
        if not valid:
            all_valid = False
    
    return all_valid

def main():
    """Main function to prepare data for analysis."""
    # Set up logging
    setup_logging()
    
    # Parse arguments
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Validate input files if requested
    if args.validate:
        if not validate_input_files(args.input_dir):
            logger.error("Input data validation failed. Please check the input files.")
            return
    
    logger.info("Data preparation complete.")

if __name__ == '__main__':
    main()
