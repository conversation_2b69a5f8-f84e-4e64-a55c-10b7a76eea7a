#!/usr/bin/env python3
"""
Test script to verify the chunked processing fix for large atmospheric datasets.

This script creates a large synthetic dataset to simulate the memory issues
and verify that the chunked processing approach works correctly.
"""

import os
import sys
import numpy as np
import xarray as xr
import tempfile
import logging

# Add src to path
sys.path.insert(0, 'src')

from models.precipitation import PrecipitationAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_large_test_data():
    """Create large test data to simulate memory issues."""
    
    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    logger.info(f"Creating large test data in: {temp_dir}")
    
    # Large grid parameters to simulate real scenario
    # Precipitation grid (high resolution)
    precip_ny, precip_nx = 100, 120  # Smaller than real but still substantial
    # Atmospheric grid (lower resolution) 
    atmos_ny, atmos_nx = 25, 30   # Much smaller to test interpolation
    
    n_days = 5000  # Large number of days to test chunking
    
    # Create sample_data directory
    sample_data_dir = os.path.join(temp_dir, "sample_data")
    os.makedirs(sample_data_dir, exist_ok=True)
    
    # Create precipitation coordinates (high resolution)
    precip_lat_coords = np.linspace(35, 45, precip_ny)
    precip_lon_coords = np.linspace(-120, -110, precip_nx)
    time_coords = np.arange(n_days)
    
    # Create atmospheric coordinates (lower resolution, same geographic extent)
    atmos_lat_coords = np.linspace(35, 45, atmos_ny)
    atmos_lon_coords = np.linspace(-120, -110, atmos_nx)
    
    logger.info(f"Creating precipitation data: {n_days} days, {precip_ny}x{precip_nx} grid")
    
    # Create precipitation data in chunks to avoid memory issues during creation
    chunk_size = 1000
    precip_chunks = []
    
    np.random.seed(42)
    for i in range(0, n_days, chunk_size):
        end_i = min(i + chunk_size, n_days)
        chunk_data = np.random.gamma(2, 3, (end_i - i, precip_ny, precip_nx))
        precip_chunks.append(chunk_data)
    
    precip_data = np.concatenate(precip_chunks, axis=0)
    
    precip_ds = xr.Dataset(
        {"precip": (["time", "y", "x"], precip_data)},
        coords={"time": time_coords, "y": precip_lat_coords, "x": precip_lon_coords}
    )
    precip_path = os.path.join(temp_dir, "precip.nc")
    precip_ds.to_netcdf(precip_path)
    
    # Create cluster data (matches precipitation time)
    cluster_data = {}
    for k in range(2, 4):  # Just test k=2,3 to save time
        cluster_assignments = np.random.randint(0, k, n_days)
        cluster_data[f"k={k}"] = (["time"], cluster_assignments)
    
    cluster_ds = xr.Dataset(cluster_data, coords={"time": time_coords})
    cluster_path = os.path.join(temp_dir, "clusters.nc")
    cluster_ds.to_netcdf(cluster_path)
    
    logger.info(f"Creating atmospheric data: {n_days} days, {atmos_ny}x{atmos_nx} grid")
    
    # Create atmospheric data files with DIFFERENT grid resolution
    # H500 (geopotential height at 500 hPa)
    h500_chunks = []
    for i in range(0, n_days, chunk_size):
        end_i = min(i + chunk_size, n_days)
        chunk_data = np.random.normal(5500, 100, (end_i - i, atmos_ny, atmos_nx))
        h500_chunks.append(chunk_data)
    
    h500_data = np.concatenate(h500_chunks, axis=0)
    h500_ds = xr.Dataset(
        {"z": (["time", "pressure_level", "latitude", "longitude"], h500_data[:, np.newaxis, :, :])},
        coords={
            "time": time_coords,
            "pressure_level": [500],
            "latitude": atmos_lat_coords,
            "longitude": atmos_lon_coords
        }
    )
    h500_path = os.path.join(sample_data_dir, "h_500_CONUS_1979_2020.nc")
    h500_ds.to_netcdf(h500_path)
    
    # MSLP (mean sea level pressure) - using different coordinate names
    mslp_chunks = []
    for i in range(0, n_days, chunk_size):
        end_i = min(i + chunk_size, n_days)
        chunk_data = np.random.normal(101325, 1000, (end_i - i, atmos_ny, atmos_nx))
        mslp_chunks.append(chunk_data)
    
    mslp_data = np.concatenate(mslp_chunks, axis=0)
    mslp_ds = xr.Dataset(
        {"msl": (["time", "lat", "lon"], mslp_data)},
        coords={"time": time_coords, "lat": atmos_lat_coords, "lon": atmos_lon_coords}
    )
    mslp_path = os.path.join(sample_data_dir, "era5_mslp_CONUS_1979_2020.nc")
    mslp_ds.to_netcdf(mslp_path)
    
    logger.info("Test data creation completed")
    return temp_dir, precip_path, cluster_path

def test_chunked_processing():
    """Test the chunked processing approach."""
    
    logger.info("Creating large test data...")
    temp_dir, precip_path, cluster_path = create_large_test_data()
    
    try:
        # Initialize analyzer
        output_path = os.path.join(temp_dir, "output_chunked.nc")
        
        logger.info("Initializing PrecipitationAnalyzer with chunked processing...")
        analyzer = PrecipitationAnalyzer(
            precip_path=precip_path,
            cluster_path=cluster_path,
            output_path=output_path,
            max_k=3,  # Just test k=2,3 to save time
            atmos_data_dir=os.path.join(temp_dir, "sample_data")
        )
        
        # Run the analysis
        logger.info("Running analysis with chunked atmospheric processing...")
        analyzer.run()
        
        # Check the output
        logger.info("Checking output file...")
        output_ds = xr.open_dataset(output_path)
        
        logger.info(f"Output variables: {list(output_ds.data_vars.keys())}")
        logger.info(f"Output dimensions: {dict(output_ds.dims)}")
        
        # Check for atmospheric variables
        expected_atmos_vars = ['mean_h500', 'anomaly_h500', 'mean_mslp', 'anomaly_mslp']
        
        found_atmos_vars = [var for var in expected_atmos_vars if var in output_ds.data_vars]
        logger.info(f"Found atmospheric variables: {found_atmos_vars}")
        
        if found_atmos_vars:
            logger.info("✅ SUCCESS: Chunked processing worked! Large atmospheric datasets processed.")
            
            # Check that the output has reasonable values
            for var in found_atmos_vars:
                var_data = output_ds[var]
                logger.info(f"{var} shape: {var_data.shape}")
                
                # Check for non-NaN values
                non_nan_count = np.sum(~np.isnan(var_data.values))
                total_count = var_data.size
                logger.info(f"{var} has {non_nan_count}/{total_count} non-NaN values")
                
                if non_nan_count > 0:
                    sample_values = var_data.values[~np.isnan(var_data.values)][:5]
                    logger.info(f"{var} sample values: {sample_values}")
                    
        else:
            logger.warning("⚠️  No atmospheric variables found in output")
            
        output_ds.close()
        
        logger.info("✅ Chunked processing test completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        # Clean up
        import shutil
        shutil.rmtree(temp_dir)
        logger.info("Cleaned up test data")

if __name__ == "__main__":
    test_chunked_processing()
