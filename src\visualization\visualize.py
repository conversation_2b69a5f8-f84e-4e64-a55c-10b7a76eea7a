# -*- coding: utf-8 -*-
import os
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import cartopy.crs as ccrs
import cartopy.feature as cfeature
import matplotlib.image as mpimg
import xarray as xr
import logging

logger = logging.getLogger(__name__)

class RegionalizationVisualizer:
    """Class for visualizing regionalization results."""
    
    def __init__(self, outdir, latlon_path=None):
        """
        Initialize the visualizer.
        
        Parameters:
        -----------
        outdir : str
            Output directory for plots
        latlon_path : str, optional
            Path to the lat/lon NetCDF file
        """
        self.outdir = outdir
        self.plot_dir = os.path.join(outdir, "plots")
        os.makedirs(self.plot_dir, exist_ok=True)
        
        if latlon_path:
            latlon = xr.open_dataset(latlon_path)
            self.lon2d = latlon['longitude']
            self.lat2d = latlon['latitude']
        else:
            self.lon2d = None
            self.lat2d = None
    
    def plot_region_map(self, class_map, k_name, cmap='tab20'):
        """
        Plot a single region map.
        
        Parameters:
        -----------
        class_map : numpy.ndarray
            2D array of region classifications
        k_name : str
            Name of the clustering (e.g., 'k=10')
        cmap : str, optional
            Matplotlib colormap name
        
        Returns:
        --------
        str
            Path to the saved figure
        """
        # Identify unique values (excluding nan)
        unique_vals = np.unique(class_map[~np.isnan(class_map)])
        n_classes = len(unique_vals)
        cmap_obj = plt.get_cmap(cmap, n_classes)
        norm = mcolors.BoundaryNorm(boundaries=np.arange(n_classes+1)-0.5, ncolors=n_classes)

        # Plot with Cartopy
        fig, ax = plt.subplots(figsize=(12, 12), subplot_kw={'projection': ccrs.PlateCarree()})

        mesh = ax.pcolormesh(self.lon2d, self.lat2d, class_map, cmap=cmap_obj, norm=norm, shading='auto')

        # Basemap features
        ax.coastlines(resolution='10m')
        ax.add_feature(cfeature.BORDERS.with_scale('10m'))
        ax.add_feature(cfeature.STATES.with_scale('10m'))

        ax.set_extent([-125, -66.5, 24, 50], crs=ccrs.PlateCarree())
        ax.set_title(f'{k_name} Region Map')

        # Colorbar
        cbar = plt.colorbar(mesh, ax=ax, ticks=unique_vals, shrink=0.6)
        cbar.set_label('Class Value')

        # Save figure
        out_path = os.path.join(self.plot_dir, f"{k_name.replace('=', '')}.png")
        plt.savefig(out_path, dpi=150)
        plt.close(fig)
        
        return out_path
    
    def create_comparison_grid(self, k_values, title="All Region Maps (Discrete Colors)"):
        """
        Create a grid of all region maps for comparison.
        
        Parameters:
        -----------
        k_values : list
            List of k values to include in the grid
        title : str, optional
            Title for the comparison grid
        
        Returns:
        --------
        str
            Path to the saved figure
        """
        nrows, ncols = 5, 8
        fig, axs = plt.subplots(nrows, ncols, figsize=(40, 40))

        for i, k_name in enumerate(k_values):
            img_path = os.path.join(self.plot_dir, f"{k_name.replace('=', '')}.png")
            img = mpimg.imread(img_path)
            ax = axs[i // ncols, i % ncols]
            ax.imshow(img)
            ax.axis('off')
            ax.set_title(k_name.replace('=', ''), fontsize=8)

        plt.suptitle(title, fontsize=18)
        plt.tight_layout(rect=[0, 0, 1, 0.96])
        
        out_path = os.path.join(self.plot_dir, "all_k_maps_grid.png")
        plt.savefig(out_path, dpi=400)
        plt.close(fig)
        
        return out_path
