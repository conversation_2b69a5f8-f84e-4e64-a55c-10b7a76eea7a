#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script to run precipitation regionalization analysis.
"""
import os
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"

import logging
import argparse
import numpy as np
import xarray as xr
from src.features.standardize import VariableStandardizer
from src.models.clustering import EOFClusteringAnalyzer
from src.visualization.visualize import RegionalizationVisualizer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Run precipitation regionalization analysis')
    parser.add_argument('--outdir', type=str, default='./regionalization_wo_WT',
                        help='Output directory for results')
    parser.add_argument('--maxclust', type=int, default=40,
                        help='Maximum number of clusters to consider')
    parser.add_argument('--nsim', type=int, default=100,
                        help='Number of simulations')
    parser.add_argument('--method', type=str, default='kmeans', choices=['kmeans', 'hierarchical'],
                        help='Clustering method to use (kmeans or hierarchical)')
    parser.add_argument('--n-jobs', type=int, default=None,
                        help='Number of parallel jobs (auto-adjusted for hierarchical clustering)')
    return parser.parse_args()

def main():
    """Main function to run precipitation regionalization."""
    args = parse_args()

    # Update output directory to include method
    if args.outdir == './regionalization_wo_WT':
        args.outdir = f'./regionalization_wo_WT_{args.method}'

    # Create output directory
    os.makedirs(args.outdir, exist_ok=True)

    logger.info(f"Running precipitation regionalization using {args.method} clustering")
    logger.info(f"Output directory: {args.outdir}")

    # Adjust parameters for hierarchical clustering
    if args.method == 'hierarchical':
        if args.n_jobs is None:
            args.n_jobs = 8  # Conservative default for hierarchical
        if args.nsim > 50:
            logger.warning(f"Reducing nsim from {args.nsim} to 50 for hierarchical clustering memory efficiency")
            args.nsim = 50
        logger.info(f"Using {args.n_jobs} parallel jobs for hierarchical clustering")
    else:
        if args.n_jobs is None:
            args.n_jobs = 64  # Default for K-means
    
    # Define paths and parameters
    paths = {
        "elevation": "sample_data/elevation_CONUS_CCSM_grid.nc",
        "lat": "sample_data/WRF_CCSM_lat_lon.nc",
        "lon": "sample_data/WRF_CCSM_lat_lon.nc",
        "ams": "sample_data/PRISM_1981_2020_Mean_AMS.nc",
    }
    
    names = {
        "elevation": "interpolated",
        "lat": "latitude",
        "lon": "longitude",
        "ams": "precip",
    }
    
    # Initialize standardizer
    logger.info("Initializing variable standardizer")
    stdzr = VariableStandardizer(variable_paths=paths, variable_names=names)
    
    # Standardize data
    logger.info("Standardizing data")
    X = stdzr.fit_transform()
    logger.info(f"Standardized data shape: {X.shape}")
    
    # Initialize and run clustering analyzer
    logger.info(f"Initializing clustering analyzer with {args.method} method")
    analyzer = EOFClusteringAnalyzer(
        X,
        outdir=args.outdir,
        maxclust=args.maxclust,
        nsim=args.nsim,
        method=args.method
    )
    


    # Optional: Generate red noise and test CI (only for k-means)
    if args.method == 'kmeans' and args.nsim > 0:
        # Compute or load classifiability index
        logger.info("Computing classifiability index")
        analyzer.load_or_compute_ci()
        analyzer.plot_ci()
        logger.info("Generating red noise")
        analyzer.generate_red_noise()
        logger.info("Testing CI with red noise")
        analyzer.ci_red_noise_test()
        analyzer.plot_ci_ci()
    
    # Visualize results
    logger.info("Visualizing results")
    ds = xr.open_dataset(f"{args.outdir}/CI_results_{args.method}.nc")
    
    k_values = [f'k={i}' for i in range(1, args.maxclust + 1)]
    
    # Initialize visualizer
    visualizer = RegionalizationVisualizer(args.outdir, "sample_data/WRF_CCSM_lat_lon.nc")
    
    # Plot individual region maps
    for k_name in k_values:
        logger.info(f"Plotting {k_name}")
        class_map = stdzr.inverse_transform_to_grid(ds[k_name].values)
        visualizer.plot_region_map(class_map, k_name)
    
    # Create comparison grid
    logger.info("Creating comparison grid")
    visualizer.create_comparison_grid(k_values)
    
    logger.info("Precipitation regionalization complete")

if __name__ == '__main__':
    main()
