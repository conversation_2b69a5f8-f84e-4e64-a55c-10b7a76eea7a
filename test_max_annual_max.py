#!/usr/bin/env python3
"""
Test script for the new max_annual_max variable in precipitation statistics.

This script creates synthetic data with known patterns and verifies that the
max_annual_max calculation is working correctly.
"""

import os
import tempfile
import numpy as np
import xarray as xr
import pandas as pd
import logging
from src.models.precipitation import PrecipitationAnalyzer

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data_with_known_annual_maxima():
    """
    Create synthetic test data with known annual maximum patterns.
    
    Returns:
    --------
    tuple
        (temp_dir, precip_path, cluster_path, expected_max_annual_max)
    """
    temp_dir = tempfile.mkdtemp()
    logger.info(f"Creating test data in {temp_dir}")
    
    # Create synthetic data parameters
    n_years = 5
    days_per_year = 365
    n_times = n_years * days_per_year
    ny, nx = 10, 15
    
    # Create time coordinates
    start_date = pd.Timestamp('2000-01-01')
    time_coords = pd.date_range(start_date, periods=n_times, freq='D')
    
    # Create spatial coordinates
    lat_coords = np.linspace(30, 50, ny)
    lon_coords = np.linspace(-120, -100, nx)
    
    # Create synthetic precipitation data with known annual maximum patterns
    # Weather Type 0: Higher precipitation in first half of each year, max around day 100
    # Weather Type 1: Higher precipitation in second half of each year, max around day 300
    
    precip_data = np.zeros((n_times, ny, nx))
    cluster_assignments = np.zeros(n_times, dtype=int)
    
    # Track expected annual maxima for each weather type
    expected_annual_maxima = {0: [], 1: []}
    
    for year in range(n_years):
        year_start = year * days_per_year
        year_end = (year + 1) * days_per_year
        
        # Create weather type assignments for this year
        # First half: mostly WT 0, second half: mostly WT 1
        wt0_days = np.random.choice(range(year_start, year_start + 200), size=120, replace=False)
        wt1_days = np.random.choice(range(year_start + 200, year_end), size=120, replace=False)
        
        cluster_assignments[wt0_days] = 0
        cluster_assignments[wt1_days] = 1
        
        # Create precipitation patterns
        # WT 0: Peak around day 100 of year (spring maximum)
        wt0_annual_maxima = np.zeros((ny, nx))
        wt1_annual_maxima = np.zeros((ny, nx))
        
        for i, day_idx in enumerate(wt0_days):
            day_of_year = (day_idx - year_start) + 1
            # Higher values around day 100, with spatial variation
            base_precip = 10 + 15 * np.exp(-((day_of_year - 100) / 50) ** 2)
            spatial_pattern = 1 + 0.3 * np.sin(np.arange(ny)[:, None] * np.pi / ny) * np.cos(np.arange(nx) * np.pi / nx)
            precip_data[day_idx] = base_precip * spatial_pattern + np.random.normal(0, 2, (ny, nx))
            precip_data[day_idx] = np.maximum(precip_data[day_idx], 0)  # No negative precipitation
            
            # Track maximum for this weather type
            wt0_annual_maxima = np.maximum(wt0_annual_maxima, precip_data[day_idx])
        
        for i, day_idx in enumerate(wt1_days):
            day_of_year = (day_idx - year_start) + 1
            # Higher values around day 300, with different spatial pattern
            base_precip = 8 + 12 * np.exp(-((day_of_year - 300) / 40) ** 2)
            spatial_pattern = 1 + 0.4 * np.cos(np.arange(ny)[:, None] * np.pi / ny) * np.sin(np.arange(nx) * np.pi / nx)
            precip_data[day_idx] = base_precip * spatial_pattern + np.random.normal(0, 1.5, (ny, nx))
            precip_data[day_idx] = np.maximum(precip_data[day_idx], 0)  # No negative precipitation
            
            # Track maximum for this weather type
            wt1_annual_maxima = np.maximum(wt1_annual_maxima, precip_data[day_idx])
        
        # Store annual maxima for this year
        expected_annual_maxima[0].append(wt0_annual_maxima)
        expected_annual_maxima[1].append(wt1_annual_maxima)
    
    # Calculate expected max annual maximum (maximum over all years)
    expected_max_annual_max = {}
    for wt in [0, 1]:
        if expected_annual_maxima[wt]:
            # Stack all years and take maximum across years
            all_years_maxima = np.stack(expected_annual_maxima[wt], axis=0)
            expected_max_annual_max[wt] = np.max(all_years_maxima, axis=0)
        else:
            expected_max_annual_max[wt] = np.full((ny, nx), np.nan)
    
    logger.info(f"Expected max annual max - WT 0: {np.nanmean(expected_max_annual_max[0]):.2f}, WT 1: {np.nanmean(expected_max_annual_max[1]):.2f}")
    
    # Save precipitation data
    precip_ds = xr.Dataset(
        {"precip": (["time", "y", "x"], precip_data)},
        coords={"time": time_coords, "y": lat_coords, "x": lon_coords}
    )
    precip_path = os.path.join(temp_dir, "precip.nc")
    precip_ds.to_netcdf(precip_path)
    
    # Save cluster data
    cluster_data = {"k=2": (["time"], cluster_assignments)}
    cluster_ds = xr.Dataset(cluster_data, coords={"time": time_coords})
    cluster_path = os.path.join(temp_dir, "clusters.nc")
    cluster_ds.to_netcdf(cluster_path)
    
    return temp_dir, precip_path, cluster_path, expected_max_annual_max

def test_max_annual_max():
    """Test the max annual maximum precipitation calculation."""
    
    logger.info("Creating test data with known annual maximum patterns...")
    temp_dir, precip_path, cluster_path, expected_max_annual_max = create_test_data_with_known_annual_maxima()
    
    try:
        # Initialize analyzer
        output_path = os.path.join(temp_dir, "output_max_annual_max.nc")
        
        logger.info("Initializing PrecipitationAnalyzer...")
        analyzer = PrecipitationAnalyzer(
            precip_path=precip_path,
            cluster_path=cluster_path,
            output_path=output_path,
            max_k=2
        )
        
        # Run the analysis
        logger.info("Running analysis...")
        analyzer.run()
        
        # Check the output
        logger.info("Checking output file...")
        output_ds = xr.open_dataset(output_path)
        
        logger.info(f"Output variables: {list(output_ds.data_vars.keys())}")
        
        # Verify max_annual_max variable exists
        assert "max_annual_max" in output_ds.data_vars, "max_annual_max variable not found in output"
        logger.info("✓ max_annual_max variable found in output")
        
        # Check dimensions
        max_annual_max_data = output_ds["max_annual_max"]
        expected_dims = ["k", "cluster", "y", "x"]
        assert list(max_annual_max_data.dims) == expected_dims, f"Expected dims {expected_dims}, got {list(max_annual_max_data.dims)}"
        logger.info("✓ max_annual_max has correct dimensions")
        
        # Check attributes
        assert "long_name" in max_annual_max_data.attrs, "max_annual_max missing long_name attribute"
        assert "description" in max_annual_max_data.attrs, "max_annual_max missing description attribute"
        assert "units" in max_annual_max_data.attrs, "max_annual_max missing units attribute"
        logger.info("✓ max_annual_max has required attributes")
        
        # Check values for k=2
        k2_data = max_annual_max_data.sel(k=2)
        
        # Compare with expected values
        for wt in [0, 1]:
            calculated = k2_data.sel(cluster=wt).values
            expected = expected_max_annual_max[wt]
            
            # Check that calculated values are reasonable
            assert not np.all(np.isnan(calculated)), f"All values are NaN for weather type {wt}"
            
            # Check that max_annual_max >= mean_annual_max (it should be at least as large)
            mean_annual_max_data = output_ds["mean_annual_max"].sel(k=2, cluster=wt).values
            assert np.all(calculated >= mean_annual_max_data), f"max_annual_max should be >= mean_annual_max for WT {wt}"
            
            logger.info(f"✓ Weather type {wt}: max_annual_max = {np.nanmean(calculated):.2f} mm/day")
            logger.info(f"  Expected: {np.nanmean(expected):.2f} mm/day")
            logger.info(f"  Mean annual max: {np.nanmean(mean_annual_max_data):.2f} mm/day")
        
        # Verify that WT 0 and WT 1 have different patterns (they should due to different timing)
        wt0_values = k2_data.sel(cluster=0).values
        wt1_values = k2_data.sel(cluster=1).values
        
        # They should have different spatial patterns
        correlation = np.corrcoef(wt0_values.flatten(), wt1_values.flatten())[0, 1]
        logger.info(f"Spatial correlation between WT 0 and WT 1: {correlation:.3f}")
        
        logger.info("✓ All tests passed!")
        
        # Print summary statistics
        logger.info("\nSummary Statistics:")
        logger.info(f"Max annual maximum precipitation:")
        logger.info(f"  WT 0: {np.nanmean(wt0_values):.2f} ± {np.nanstd(wt0_values):.2f} mm/day")
        logger.info(f"  WT 1: {np.nanmean(wt1_values):.2f} ± {np.nanstd(wt1_values):.2f} mm/day")
        
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False
        
    finally:
        # Clean up
        import shutil
        shutil.rmtree(temp_dir)
        logger.info(f"Cleaned up temporary directory: {temp_dir}")

if __name__ == "__main__":
    logger.info("Testing max annual maximum precipitation calculation...")
    success = test_max_annual_max()
    
    if success:
        logger.info("✅ All tests passed successfully!")
    else:
        logger.error("❌ Tests failed!")
        exit(1)
