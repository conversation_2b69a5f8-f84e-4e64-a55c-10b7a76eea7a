
library(lmom)

# Data
regional_tr <- 0.110298
regional_tr3 <- 0.027859
book_tau2 <- 0.1103
book_tau3 <- 0.0279
book_tau4 <- 0.1366
book_gamma <- 0.1626

cat("R lmom Analysis\n")
cat("===============\n")

# Test with our calculated values
cat("\nUsing our calculated values:\n")
l1 <- 1.0
l2 <- regional_tr * l1
l3 <- regional_tr3 * l2
l4 <- 0.0

lmoms_our <- c(l1, l2, l3, l4)
cat("L-moments:", sprintf("%.6f", lmoms_our), "\n")

# GEV
tryCatch({
  gev_params <- pelgev(lmoms_our)
  cat("\nGEV:\n")
  cat("  xi:", sprintf("%.6f", gev_params[1]), "\n")
  cat("  alpha:", sprintf("%.6f", gev_params[2]), "\n")
  cat("  kappa:", sprintf("%.6f", gev_params[3]), "\n")
}, error = function(e) {
  cat("\nGEV: Error -", e$message, "\n")
})

# GLO
tryCatch({
  glo_params <- pelglo(lmoms_our)
  cat("\nGLO:\n")
  cat("  xi:", sprintf("%.6f", glo_params[1]), "\n")
  cat("  alpha:", sprintf("%.6f", glo_params[2]), "\n")
  cat("  kappa:", sprintf("%.6f", glo_params[3]), "\n")
}, error = function(e) {
  cat("\nGLO: Error -", e$message, "\n")
})

# GPA
tryCatch({
  gpa_params <- pelgpa(lmoms_our)
  cat("\nGPA:\n")
  cat("  xi:", sprintf("%.6f", gpa_params[1]), "\n")
  cat("  alpha:", sprintf("%.6f", gpa_params[2]), "\n")
  cat("  kappa:", sprintf("%.6f", gpa_params[3]), "\n")
}, error = function(e) {
  cat("\nGPA: Error -", e$message, "\n")
})

# PE3
tryCatch({
  pe3_params <- pelpe3(lmoms_our)
  cat("\nPE3:\n")
  cat("  mu:", sprintf("%.6f", pe3_params[1]), "\n")
  cat("  sigma:", sprintf("%.6f", pe3_params[2]), "\n")
  cat("  gamma:", sprintf("%.6f", pe3_params[3]), "\n")
  gamma_diff <- abs(pe3_params[3] - book_gamma)
  cat("  PE3 γ diff from book:", sprintf("%.6f", gamma_diff), "\n")
}, error = function(e) {
  cat("\nPE3: Error -", e$message, "\n")
})

# GNO
tryCatch({
  gno_params <- pelgno(lmoms_our)
  cat("\nGNO:\n")
  cat("  xi:", sprintf("%.6f", gno_params[1]), "\n")
  cat("  alpha:", sprintf("%.6f", gno_params[2]), "\n")
  cat("  kappa:", sprintf("%.6f", gno_params[3]), "\n")
}, error = function(e) {
  cat("\nGNO: Error -", e$message, "\n")
})

# KAPPA (if available)
tryCatch({
  kap_params <- pelkap(lmoms_our)
  cat("\nKAPPA:\n")
  cat("  xi:", sprintf("%.6f", kap_params[1]), "\n")
  cat("  alpha:", sprintf("%.6f", kap_params[2]), "\n")
  cat("  kappa:", sprintf("%.6f", kap_params[3]), "\n")
  cat("  h:", sprintf("%.6f", kap_params[4]), "\n")
}, error = function(e) {
  cat("\nKAPPA: Error -", e$message, "\n")
})

# Test with book values
cat("\n\nUsing book values:\n")
l1_book <- 1.0
l2_book <- book_tau2 * l1_book
l3_book <- book_tau3 * l2_book
l4_book <- 0.0

lmoms_book <- c(l1_book, l2_book, l3_book, l4_book)
cat("L-moments:", sprintf("%.6f", lmoms_book), "\n")

# PE3 with book values
tryCatch({
  pe3_book <- pelpe3(lmoms_book)
  cat("\nPE3 (book values):\n")
  cat("  mu:", sprintf("%.6f", pe3_book[1]), "\n")
  cat("  sigma:", sprintf("%.6f", pe3_book[2]), "\n")
  cat("  gamma:", sprintf("%.6f", pe3_book[3]), "\n")
  gamma_diff <- abs(pe3_book[3] - book_gamma)
  cat("  PE3 γ diff from book:", sprintf("%.6f", gamma_diff), "\n")
}, error = function(e) {
  cat("\nPE3 (book): Error -", e$message, "\n")
})
