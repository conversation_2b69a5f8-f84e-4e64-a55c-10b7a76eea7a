#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script to generate precipitation statistics for weather types.
"""
import os
import logging
import argparse
from src.models.precipitation import PrecipitationAnalyzer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Generate precipitation statistics for weather types')
    parser.add_argument('--precip-path', type=str, default='sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc',
                        help='Path to precipitation NetCDF file')
    parser.add_argument('--cluster-path', type=str, default='CONUS_ERA5/CI_results.nc',
                        help='Path to cluster results NetCDF file')
    parser.add_argument('--output-path', type=str, default='cluster_precip_stats.nc',
                        help='Path to save output statistics')
    return parser.parse_args()

def main():
    """Main function to generate precipitation statistics."""
    args = parse_args()
    
    # Initialize precipitation analyzer
    logger.info("Initializing precipitation analyzer")
    analyzer = PrecipitationAnalyzer(
        precip_path=args.precip_path,
        cluster_path=args.cluster_path,
        output_path=args.output_path
    )
    
    # Run analysis
    logger.info("Running precipitation analysis")
    analyzer.run()
    
    logger.info(f"Precipitation statistics saved to {args.output_path}")

if __name__ == '__main__':
    main()
