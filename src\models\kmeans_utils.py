# -*- coding: utf-8 -*-
import numpy as np
from sklearn.cluster import MiniBatchKMeans as KMeans
from joblib import Parallel, delayed
import logging

logger = logging.getLogger(__name__)

def run_kmeans_simulation(X, PC, nclus, c, sim_index):
    """
    Run a single KMeans simulation and return cluster assignments and cluster centroids.
    
    Parameters:
    -----------
    X : numpy.ndarray
        Input data matrix
    PC : numpy.ndarray
        Principal components
    nclus : int
        Number of clusters
    c : int
        Number of features
    sim_index : int
        Simulation index
        
    Returns:
    --------
    tuple
        (cluster labels, standardized centroids)
    """
    mean_cluster = np.zeros((nclus, c))
    kmeans = KMeans(n_clusters=nclus, init="k-means++", batch_size=125, n_init=1, max_iter=1000)
    labels = kmeans.fit_predict(X)

    for j in range(nclus):
        points_in_cluster = PC[labels == j]
        if points_in_cluster.shape[0] > 1:
            mean_cluster[j, :] = np.mean(points_in_cluster, axis=0)
        elif points_in_cluster.shape[0] == 1:
            mean_cluster[j, :] = points_in_cluster[0]
        else:
            mean_cluster[j, :] = np.nan

    # Standardize centroids
    nan_mean = np.nanmean(mean_cluster.T, axis=0)
    nan_std = np.nanstd(mean_cluster.T, axis=0, ddof=1)
    mean_cluster2 = (mean_cluster.T - nan_mean) / nan_std

    return labels, mean_cluster2.flatten("F")


def kmeans_ci(X=None, stand=None, weighting=None, prop=None, nclus=None, nsim=None, n_jobs=64):
    """
    Perform K-means clustering with classifiability index calculation.

    Parameters:
    -----------
    X : numpy.ndarray
        Input data matrix
    stand : bool, optional
        Whether to standardize the data
    weighting : bool, optional
        Whether to apply weighting (currently unused)
    prop : float, optional
        Proportion of variance to retain in EOF prefiltering
    nclus : int
        Number of clusters
    nsim : int
        Number of simulations
    n_jobs : int, optional
        Number of parallel jobs

    Returns:
    --------
    tuple
        (cluster labels, classifiability index)
    """

    if stand:
        nan_mean = np.nanmean(X, axis=0)
        nan_std = np.nanstd(X, axis=0, ddof=1)
        X = (X - nan_mean) / nan_std

    r, c = X.shape

    if prop:
        logger.info("Performing EOF prefiltering using randomized SVD...")
        # Estimate number of components needed
        U_full, S_full, _ = np.linalg.svd(X, full_matrices=False)
        s = S_full * 2
        sc = s / np.sum(s)
        k_eof = np.searchsorted(np.cumsum(sc), prop) + 1
        # Use randomized SVD for speed
        U, S, Vt = randomized_svd(X, n_components=k_eof, random_state=0)
        PC = U @ np.diag(S)
        logger.info(f"Pre-filtering using EOF retaining the first {k_eof} components done.")
    else:
        PC = X
        logger.info("No EOF prefiltering ...")

    logger.info(f"K-means clustering with {nclus} clusters begins ...")

    results = Parallel(n_jobs=n_jobs)(
        delayed(run_kmeans_simulation)(X, PC, nclus, c, i) for i in range(nsim)
    )

    k = np.array([res[0] for res in results]).T  # shape (r, nsim)
    MC = np.stack([res[1] for res in results], axis=1)  # shape (nclus * c, nsim)

    ACCmax = np.zeros((nclus, nsim))

    for i in range(nclus):
        for j in range(nsim):
            sample1 = MC[(i * c):(i + 1) * c, j]
            other = np.delete(MC, j, axis=1).reshape(c, (nsim - 1) * nclus, order="F")
            sample1[np.isnan(sample1)] = 0
            other[np.isnan(other)] = 0
            ACC = (1 / (c - 1)) * sample1.conj().T @ other
            ACC = ACC.reshape(nclus, nsim - 1, order="F")
            ACCmax[i, j] = np.mean(np.max(ACC, axis=0))

    part = np.argmax(np.mean(ACCmax, axis=0))
    CI = np.mean(ACCmax)
    K = k[:, part]

    return K, CI

def ar1rand(X=None, nsim=None):
    """
    Red-noise random simulation of 'nsim' time series of the
    same length as the input vector 'X' and having the same
    one-order auto-correlation and mean and variance as 'X'
    
    Parameters:
    -----------
    X : numpy.ndarray
        Input time series
    nsim : int
        Number of simulations
        
    Returns:
    --------
    numpy.ndarray
        Simulated time series
    """
    X = X.flatten('F')
    n = len(X)
    c = np.corrcoef(X[0:n-1], X[1:n])[0, 1]
    d = 1 - c
    Y = np.zeros((n, nsim))

    Y[0,:] = np.random.randn(1, nsim)
    Z = np.random.randn(n, nsim)
    Z = scale_mean_var(Z, copy(X[1:n].conj().T, nsim).conj().T)

    for j in range(1, n):
        Y[j, :] = (Y[j-1, :] * c) + (Z[j, :] * d)

    Y = scale_mean_var(Y, copy(X.conj().T, nsim).conj().T)
    
    return Y


def copy(y=None, N=None):
    """
    Copy a matrix N times.
    
    Parameters:
    -----------
    y : numpy.ndarray
        Input matrix
    N : int
        Number of copies
        
    Returns:
    --------
    numpy.ndarray
        Copied matrix
    """
    NR = 1
    NC = len(y)

    x = y.conj().T
    x = x.flatten('F')
    x = (x[:, None]) @ (np.ones((1, N)))
    x = x.reshape(NC, NR * N, order='F')
    x = x.conj().T

    return x


def scale_mean_var(X, Y):
    """
    Scale the columns of X to match the mean and variance of Y.
    
    Parameters:
    -----------
    X : numpy.ndarray
        Matrix to scale
    Y : numpy.ndarray
        Reference matrix
        
    Returns:
    --------
    numpy.ndarray
        Scaled matrix
    """
    nr, nc = X.shape

    my = np.mean(Y, axis=0)
    sy = np.std(Y, axis=0, ddof=1)
    mx = np.mean(X, axis=0)
    sx = np.std(X, axis=0, ddof=1)

    Z = (X @ np.diag(sy/sx))
    dm = np.mean(Z, axis=0) - my
    dm = np.ones((nr, 1)) * dm
    Z = Z - dm

    return Z
