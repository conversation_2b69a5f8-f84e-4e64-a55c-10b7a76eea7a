# -*- coding: utf-8 -*-
"""
Custom exceptions for the weather typing and precipitation regionalization project.
"""

class WeatherTypingError(Exception):
    """Base exception for weather typing errors."""
    pass

class DataError(WeatherTypingError):
    """Exception raised for data-related errors."""
    pass

class ConfigurationError(WeatherTypingError):
    """Exception raised for configuration-related errors."""
    pass

class ProcessingError(WeatherTypingError):
    """Exception raised for processing-related errors."""
    pass

class ValidationError(WeatherTypingError):
    """Exception raised for validation-related errors."""
    pass

class ClusteringError(WeatherTypingError):
    """Exception raised for clustering-related errors."""
    pass

class VisualizationError(WeatherTypingError):
    """Exception raised for visualization-related errors."""
    pass

class InputError(WeatherTypingError):
    """Exception raised for input-related errors."""
    pass

class OutputError(WeatherTypingError):
    """Exception raised for output-related errors."""
    pass

class MissingDataError(DataError):
    """Exception raised when required data is missing."""
    pass

class InvalidDataError(DataError):
    """Exception raised when data is invalid."""
    pass

class InsufficientDataError(DataError):
    """Exception raised when there is not enough data."""
    pass
