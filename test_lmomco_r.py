#!/usr/bin/env python3
"""
Test script for lmomco R package using rpy2.
Calculates distribution parameters using the same tr and tr3 values as the lmoments3 test.
"""

import numpy as np
import rpy2.robjects as robjects
from rpy2.robjects import pandas2ri
from rpy2.robjects.packages import importr
import pandas as pd

# Activate automatic conversion between pandas and R dataframes
pandas2ri.activate()

def setup_r_environment():
    """Setup R environment and install/load required packages"""
    
    # Import R's utils for package installation
    utils = importr('utils')
    base = importr('base')
    
    print("Setting up R environment...")
    
    # Check if lmomco is installed, if not install it
    try:
        lmomco = importr('lmomco')
        print("✓ lmomco package already installed")
    except:
        print("Installing lmomco package...")
        utils.install_packages('lmomco')
        lmomco = importr('lmomco')
        print("✓ lmomco package installed successfully")
    
    return lmomco, base

def test_lmomco_distributions():
    """
    Test lmomco R package with provided tr and tr3 values.
    Calculate parameters for each distribution using l1=1, tr, and tr3.
    """
    
    # Setup R environment
    lmomco, base = setup_r_environment()
    
    # Your provided data
    tr_values = [.1209, .0915, .1124, .1032, .0967, .1328, .1008, .1143, .1107, .1179, 
                 .1308, .1119, .1018, .1025, .1054, .1174, .1115, .1003, .1046]
    
    tr3_values = [.0488, .0105, .0614, .0417, -.0134, -.0176, .0943, .0555, .0478, .0492, 
                  .0940, -.0429, .0435, .0182, -.0224, .0124, -.0346, .0446, .0128]
    
    rec_length = [98, 59, 90, 61, 65, 86, 78, 72, 67, 99, 49, 61, 69, 73, 70, 66, 59, 74, 82]
    
    print("LMOMCO R PACKAGE TEST")
    print("=" * 50)
    print(f"Number of stations: {len(tr_values)}")
    print(f"Total record length: {sum(rec_length)}")
    
    # Calculate weighted regional L-moment ratios
    tr_array = np.array(tr_values)
    tr3_array = np.array(tr3_values)
    rec_array = np.array(rec_length)
    
    # Regional L-moment ratios (weighted averages)
    regional_tr = (tr_array * rec_array).sum() / rec_array.sum()
    regional_tr3 = (tr3_array * rec_array).sum() / rec_array.sum()
    
    print(f"\nRegional L-moment ratios (weighted):")
    print(f"  TR (L-CV): {regional_tr:.6f}")
    print(f"  TR3 (L-Skewness): {regional_tr3:.6f}")
    
    # Create L-moment object in R
    # lmomco expects L-moments in the format: [L1, L2, T3, T4, T5]
    # where T3 = L3/L2, T4 = L4/L2, etc.
    l1 = 1.0
    l2 = regional_tr * l1  # L2 = TR * L1
    t3 = regional_tr3      # T3 = L3/L2
    t4 = 0.0              # Set T4 to 0 for now
    
    print(f"\nL-moments for fitting:")
    print(f"  L1 = {l1:.6f}")
    print(f"  L2 = {l2:.6f}")
    print(f"  T3 = {t3:.6f}")
    print(f"  T4 = {t4:.6f}")
    
    # Convert to R vectors
    robjects.r('lmoms <- c({}, {}, {}, {})'.format(l1, l2, t3, t4))
    robjects.r('names(lmoms) <- c("L1", "L2", "T3", "T4")')
    
    print(f"\n{'='*60}")
    print("DISTRIBUTION FITTING RESULTS (R lmomco)")
    print(f"{'='*60}")
    
    # Available distributions in lmomco
    distributions = {
        'kappa': 'kap',      # 4-parameter Kappa
        'gev': 'gev',        # Generalized Extreme Value
        'glo': 'glo',        # Generalized Logistic
        'gpa': 'gpa',        # Generalized Pareto
        'pe3': 'pe3',        # Pearson Type III
        'gno': 'gno',        # Generalized Normal
        'exp': 'exp',        # Exponential
        'gam': 'gam',        # Gamma
        'nor': 'nor',        # Normal
        'wei': 'wei'         # Weibull
    }
    
    results = {}
    
    for dist_name, dist_code in distributions.items():
        try:
            print(f"\n{dist_name.upper()} Distribution:")
            print("-" * 30)
            
            # Fit distribution parameters using lmomco
            robjects.r(f'params_{dist_code} <- lmom2par(lmoms, type="{dist_code}")')
            
            # Get the parameters
            params_r = robjects.r(f'params_{dist_code}')
            
            # Extract parameter values
            if hasattr(params_r, 'rx2'):
                para = params_r.rx2('para')
                if para is not None:
                    params_dict = {f'param_{i+1}': float(para[i]) for i in range(len(para))}
                    print(f"Parameters: {params_dict}")
                    
                    # Calculate theoretical L-moments from fitted parameters
                    robjects.r(f'theo_lmoms <- parlmom(params_{dist_code})')
                    theo_lmoms = robjects.r(f'theo_lmoms')
                    
                    if theo_lmoms is not None and len(theo_lmoms) >= 4:
                        theo_l1 = float(theo_lmoms[0])
                        theo_l2 = float(theo_lmoms[1])
                        theo_t3 = float(theo_lmoms[2])
                        theo_t4 = float(theo_lmoms[3])
                        
                        print(f"Theoretical L-moments:")
                        print(f"  L1 = {theo_l1:.6f}")
                        print(f"  L2 = {theo_l2:.6f}")
                        print(f"  T3 = {theo_t3:.6f}")
                        print(f"  T4 = {theo_t4:.6f}")
                        
                        # Calculate goodness of fit
                        tr_error = abs(theo_l2/theo_l1 - regional_tr) if theo_l1 != 0 else float('inf')
                        t3_error = abs(theo_t3 - regional_tr3)
                        
                        print(f"Goodness of fit:")
                        print(f"  |TR_fitted - TR_target| = {tr_error:.6f}")
                        print(f"  |T3_fitted - T3_target| = {t3_error:.6f}")
                        
                        results[dist_name] = {
                            'parameters': params_dict,
                            'theoretical_lmoms': [theo_l1, theo_l2, theo_t3, theo_t4],
                            'tr_error': tr_error,
                            't3_error': t3_error,
                            'combined_error': tr_error + t3_error
                        }
                    else:
                        print("Could not calculate theoretical L-moments")
                else:
                    print("No parameters returned")
            else:
                print("Parameter extraction failed")
                
        except Exception as e:
            print(f"Error fitting {dist_name}: {str(e)}")
            results[dist_name] = {'error': str(e)}
    
    # Find best fitting distribution
    print(f"\n{'='*60}")
    print("BEST FIT ANALYSIS (R lmomco)")
    print(f"{'='*60}")
    
    valid_results = {k: v for k, v in results.items() if 'error' not in v}
    
    if valid_results:
        # Sort by combined error
        sorted_results = sorted(valid_results.items(), key=lambda x: x[1]['combined_error'])
        
        print("Distributions ranked by goodness of fit (lower error = better):")
        for i, (dist_name, result) in enumerate(sorted_results, 1):
            print(f"{i}. {dist_name.upper()}: Combined error = {result['combined_error']:.6f}")
        
        if sorted_results:
            best_dist = sorted_results[0]
            print(f"\nBest fitting distribution: {best_dist[0].upper()}")
            print(f"Parameters: {best_dist[1]['parameters']}")
    
    # Test quantile calculations
    print(f"\n{'='*60}")
    print("QUANTILE CALCULATIONS (R lmomco)")
    print(f"{'='*60}")
    
    probabilities = [0.01, 0.05, 0.1, 0.25, 0.5, 0.75, 0.9, 0.95, 0.99]
    
    # Calculate quantiles for the best few distributions
    for dist_name in ['kappa', 'gev', 'glo']:
        if dist_name in valid_results:
            try:
                print(f"\n{dist_name.upper()} Distribution Quantiles:")
                dist_code = distributions[dist_name]
                
                for p in probabilities:
                    robjects.r(f'quantile_val <- qlmomco({p}, params_{dist_code})')
                    q_val = float(robjects.r('quantile_val')[0])
                    print(f"  P({p:4.2f}) = {q_val:8.4f}")
                    
            except Exception as e:
                print(f"Error calculating quantiles for {dist_name}: {e}")

if __name__ == "__main__":
    test_lmomco_distributions()
