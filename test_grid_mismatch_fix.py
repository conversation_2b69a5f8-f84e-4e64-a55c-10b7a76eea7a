#!/usr/bin/env python3
"""
Test script to verify the grid mismatch fix for atmospheric variables.

This script creates test data with different grid resolutions to simulate
the real-world scenario where atmospheric data (121x281) and precipitation 
data (515x599) have different spatial resolutions.
"""

import os
import sys
import numpy as np
import xarray as xr
import tempfile
import logging

# Add src to path
sys.path.insert(0, 'src')

from models.precipitation import PrecipitationAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_mismatched_grid_data():
    """Create test data with mismatched grid resolutions."""
    
    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    logger.info(f"Creating test data in: {temp_dir}")
    
    # Different grid parameters to simulate real scenario
    # Precipitation grid (high resolution)
    precip_ny, precip_nx = 50, 60  # Simulating 515x599
    # Atmospheric grid (lower resolution) 
    atmos_ny, atmos_nx = 12, 28   # Simulating 121x281
    
    n_days = 730  # 2 years
    
    # Create sample_data directory
    sample_data_dir = os.path.join(temp_dir, "sample_data")
    os.makedirs(sample_data_dir, exist_ok=True)
    
    # Create precipitation coordinates (high resolution)
    precip_lat_coords = np.linspace(35, 45, precip_ny)
    precip_lon_coords = np.linspace(-120, -110, precip_nx)
    time_coords = np.arange(n_days)
    
    # Create atmospheric coordinates (lower resolution, same geographic extent)
    atmos_lat_coords = np.linspace(35, 45, atmos_ny)
    atmos_lon_coords = np.linspace(-120, -110, atmos_nx)
    
    # Create precipitation data
    np.random.seed(42)
    precip_data = np.random.gamma(2, 3, (n_days, precip_ny, precip_nx))
    
    precip_ds = xr.Dataset(
        {"precip": (["time", "y", "x"], precip_data)},
        coords={"time": time_coords, "y": precip_lat_coords, "x": precip_lon_coords}
    )
    precip_path = os.path.join(temp_dir, "precip.nc")
    precip_ds.to_netcdf(precip_path)
    
    # Create cluster data (matches precipitation time)
    cluster_data = {}
    for k in range(2, 6):
        cluster_assignments = np.random.randint(0, k, n_days)
        cluster_data[f"k={k}"] = (["time"], cluster_assignments)
    
    cluster_ds = xr.Dataset(cluster_data, coords={"time": time_coords})
    cluster_path = os.path.join(temp_dir, "clusters.nc")
    cluster_ds.to_netcdf(cluster_path)
    
    # Create atmospheric data files with DIFFERENT grid resolution
    logger.info(f"Creating atmospheric data: {atmos_ny}x{atmos_nx} vs precipitation: {precip_ny}x{precip_nx}")
    
    # H500 (geopotential height at 500 hPa) - using common coordinate names
    h500_data = np.random.normal(5500, 100, (n_days, atmos_ny, atmos_nx))
    h500_ds = xr.Dataset(
        {"z": (["time", "pressure_level", "latitude", "longitude"], h500_data[:, np.newaxis, :, :])},
        coords={
            "time": time_coords,
            "pressure_level": [500],
            "latitude": atmos_lat_coords,
            "longitude": atmos_lon_coords
        }
    )
    h500_path = os.path.join(sample_data_dir, "h_500_CONUS_1979_2020.nc")
    h500_ds.to_netcdf(h500_path)
    
    # MSLP (mean sea level pressure) - using different coordinate names to test mapping
    mslp_data = np.random.normal(101325, 1000, (n_days, atmos_ny, atmos_nx))
    mslp_ds = xr.Dataset(
        {"msl": (["time", "lat", "lon"], mslp_data)},
        coords={"time": time_coords, "lat": atmos_lat_coords, "lon": atmos_lon_coords}
    )
    mslp_path = os.path.join(sample_data_dir, "era5_mslp_CONUS_1979_2020.nc")
    mslp_ds.to_netcdf(mslp_path)
    
    # U850 (zonal wind at 850 hPa)
    u850_data = np.random.normal(0, 10, (n_days, atmos_ny, atmos_nx))
    u850_ds = xr.Dataset(
        {"u": (["time", "pressure_level", "latitude", "longitude"], u850_data[:, np.newaxis, :, :])},
        coords={
            "time": time_coords,
            "pressure_level": [850],
            "latitude": atmos_lat_coords,
            "longitude": atmos_lon_coords
        }
    )
    u850_path = os.path.join(sample_data_dir, "u_850_CONUS_1979_2020.nc")
    u850_ds.to_netcdf(u850_path)
    
    # V850 (meridional wind at 850 hPa)
    v850_data = np.random.normal(0, 10, (n_days, atmos_ny, atmos_nx))
    v850_ds = xr.Dataset(
        {"v": (["time", "pressure_level", "latitude", "longitude"], v850_data[:, np.newaxis, :, :])},
        coords={
            "time": time_coords,
            "pressure_level": [850],
            "latitude": atmos_lat_coords,
            "longitude": atmos_lon_coords
        }
    )
    v850_path = os.path.join(sample_data_dir, "v_850_CONUS_1979_2020.nc")
    v850_ds.to_netcdf(v850_path)
    
    return temp_dir, precip_path, cluster_path

def test_grid_mismatch_fix():
    """Test the grid mismatch fix."""
    
    logger.info("Creating test data with mismatched grids...")
    temp_dir, precip_path, cluster_path = create_mismatched_grid_data()
    
    try:
        # Initialize analyzer
        output_path = os.path.join(temp_dir, "output_interpolated.nc")
        
        logger.info("Initializing PrecipitationAnalyzer...")
        analyzer = PrecipitationAnalyzer(
            precip_path=precip_path,
            cluster_path=cluster_path,
            output_path=output_path,
            max_k=5,
            atmos_data_dir=os.path.join(temp_dir, "sample_data")
        )
        
        # Run the analysis
        logger.info("Running analysis with grid interpolation...")
        analyzer.run()
        
        # Check the output
        logger.info("Checking output file...")
        output_ds = xr.open_dataset(output_path)
        
        logger.info(f"Output variables: {list(output_ds.data_vars.keys())}")
        logger.info(f"Output dimensions: {dict(output_ds.dims)}")
        
        # Check for atmospheric variables
        expected_atmos_vars = ['mean_h500', 'anomaly_h500', 'mean_mslp', 'anomaly_mslp', 
                              'mean_u850', 'anomaly_u850', 'mean_v850', 'anomaly_v850']
        
        found_atmos_vars = [var for var in expected_atmos_vars if var in output_ds.data_vars]
        logger.info(f"Found atmospheric variables: {found_atmos_vars}")
        
        if found_atmos_vars:
            logger.info("✅ SUCCESS: Grid interpolation worked! Atmospheric variables calculated.")
            
            # Check that the output has the correct spatial dimensions (matching precipitation)
            for var in found_atmos_vars[:2]:
                var_shape = output_ds[var].shape
                logger.info(f"{var} shape: {var_shape}")
                
                # Should match precipitation grid dimensions
                expected_spatial_shape = (output_ds.dims['y'], output_ds.dims['x'])
                actual_spatial_shape = var_shape[-2:]  # Last two dimensions
                
                if actual_spatial_shape == expected_spatial_shape:
                    logger.info(f"✅ {var} has correct spatial dimensions: {actual_spatial_shape}")
                else:
                    logger.error(f"❌ {var} has wrong spatial dimensions: {actual_spatial_shape}, expected: {expected_spatial_shape}")
                    
        else:
            logger.warning("⚠️  No atmospheric variables found in output")
            
        output_ds.close()
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        # Clean up
        import shutil
        shutil.rmtree(temp_dir)
        logger.info("Cleaned up test data")

if __name__ == "__main__":
    test_grid_mismatch_fix()
