# -*- coding: utf-8 -*-
import xarray as xr
import numpy as np
import logging

logger = logging.getLogger(__name__)

class PrecipitationAnalyzer:
    """
    Class for analyzing precipitation data in relation to weather types.
    """
    def __init__(self, precip_path, cluster_path, output_path="cluster_precip_stats.nc", max_k=35, chunk_size=None,
                 atmos_data_dir="sample_data"):
        """
        Initialize the precipitation analyzer.

        Parameters:
        -----------
        precip_path : str
            Path to the precipitation NetCDF file
        cluster_path : str
            Path to the cluster results NetCDF file
        output_path : str, optional
            Path to save the output statistics
        max_k : int, optional
            Maximum number of clusters to process (default: 35)
        chunk_size : int, optional
            Chunk size for processing large grids (default: auto-detect)
        atmos_data_dir : str, optional
            Directory containing atmospheric data files (default: "sample_data")
        """
        self.precip_path = precip_path
        self.cluster_path = cluster_path
        self.output_path = output_path
        self.max_k = max_k
        self.atmos_data_dir = atmos_data_dir

        # Performance settings
        self.chunk_size = chunk_size

        # Load data
        logger.info(f"Loading data (max_k={max_k})")
        self.cluster_ds = xr.open_dataset(cluster_path)
        self.ds_pr = xr.open_dataset(precip_path)
        self.precip = self.ds_pr["precip"]  # dimensions: (time, y, x)

        # Load atmospheric data if available
        self._load_atmospheric_data()

        # Initialize storage arrays
        self._initialize_arrays()

        logger.info(f"Initialized PrecipitationAnalyzer with max_k={self.max_k}, grid: {self.ny}x{self.nx}")

    def _load_atmospheric_data(self):
        """
        Load atmospheric data files if available.
        """
        import os

        # Define atmospheric data file paths and variable names
        self.atmos_files = {
            'h500': os.path.join(self.atmos_data_dir, 'h500_1979_2020_interpolated_CCSM.nc'),
            'mslp': os.path.join(self.atmos_data_dir, 'mslp_1979_2020_interpolated_CCSM.nc'),
            'u850': os.path.join(self.atmos_data_dir, 'u850_1979_2020_interpolated_CCSM.nc'),
            'v850': os.path.join(self.atmos_data_dir, 'v850_1979_2020_interpolated_CCSM.nc')
        }

        self.atmos_varnames = {
            'h500': 'z',
            'mslp': 'msl',
            'u850': 'u',
            'v850': 'v'
        }

        self.atmos_levels = {
            'h500': 500,
            'mslp': None,
            'u850': 850,
            'v850': 850
        }

        # Try to load atmospheric data
        self.atmos_data = {}
        self.has_atmos_data = False

        for var_key, file_path in self.atmos_files.items():
            if os.path.exists(file_path):
                try:
                    logger.info(f"Loading atmospheric data: {var_key} from {file_path}")
                    ds = xr.open_dataset(file_path)
                    var_name = self.atmos_varnames[var_key]
                    level = self.atmos_levels[var_key]

                    if level is not None and 'pressure_level' in ds.dims:
                        # Select specific pressure level
                        data = ds[var_name].sel(pressure_level=level)
                    else:
                        # Use data as is (e.g., for MSLP)
                        data = ds[var_name]

                    self.atmos_data[var_key] = data
                    self.has_atmos_data = True
                    logger.info(f"Successfully loaded {var_key}")

                except Exception as e:
                    logger.warning(f"Failed to load {var_key} from {file_path}: {e}")
            else:
                logger.info(f"Atmospheric data file not found: {file_path}")

        if self.has_atmos_data:
            logger.info(f"Loaded {len(self.atmos_data)} atmospheric variables")
        else:
            logger.info("No atmospheric data available - will skip atmospheric calculations")

    def _initialize_arrays(self):
        """
        Initialize arrays for storing results.
        """
        # Get spatial dimensions
        spatial_dims = [d for d in self.precip.dims if d != "time"]
        if len(spatial_dims) != 2:
            raise ValueError(f"Expected 2 spatial dimensions, found {spatial_dims}")
        self.spatial_dim1, self.spatial_dim2 = spatial_dims
        
        # Get spatial sizes
        ny, nx = self.precip.sizes[self.spatial_dim1], self.precip.sizes[self.spatial_dim2]
        
        # Setup storage arrays - use dictionary for memory efficiency
        # Only allocate arrays as needed during processing
        self.results = {
            'mean_annual_max': {},
            'mean_annual_total': {},
            'mean_annual_precip': {},
            'mean_all_days': {},
            'mean_seasonal': {},
            'mean_seasonal_total': {},
            'mean_seasonal_max': {},
            'wt_frac': {},
            'wt_AMS_frac': {},
            'wt_freq_gridpoint': {},
            'wt_freq_ams_gridpoint': {}
        }

        # Add atmospheric variable storage if data is available
        if self.has_atmos_data:
            for var_key in self.atmos_data.keys():
                self.results[f'mean_{var_key}'] = {}
                self.results[f'anomaly_{var_key}'] = {}

        # Store dimensions for later use
        self.ny, self.nx = ny, nx
        
    def preprocess_time_indices(self):
        """
        Preprocess time indices to handle leap days and create seasonal indices.
        
        Returns:
        --------
        tuple
            (precip_np, year_ids, season_ids, doy, early_mask, late_mask, unique_years)
        """
        # Identify spatial and time dimensions
        ntime = self.precip.sizes["time"]
        years = np.arange(1981, 1981 + ntime // 365 + 2)  # overshoot to be safe

        # Remove leap day indices
        leap_indices = []
        day_count = 0
        for y in years:
            is_leap = y % 4 == 0 and (y % 100 != 0 or y % 400 == 0)
            days_in_year = 366 if is_leap else 365
            if is_leap:
                feb29_index = day_count + 31 + 28
                if feb29_index < ntime:
                    leap_indices.append(feb29_index)
            day_count += days_in_year

        valid_indices = np.delete(np.arange(ntime), leap_indices)
        precip_clean = self.precip.isel(time=valid_indices)

        # Now time is clean, define year index per day
        ntime_clean = precip_clean.sizes["time"]
        year_ids = np.repeat(np.arange(1981, 1981 + ntime_clean // 365), 365)
        
        # Create day of year and season indices
        doy = np.tile(np.arange(1, 366), ntime_clean // 365)  # day of year 1-365
        season_ids = np.zeros(len(doy), dtype=int)
        # Define seasons: DJF=0, MAM=1, JJA=2, SON=3
        season_ids[(doy >= 335) | (doy <= 59)] = 0  # DJF (Dec 1 - Feb 28)
        season_ids[(doy >= 60) & (doy <= 151)] = 1   # MAM (Mar 1 - May 31)
        season_ids[(doy >= 152) & (doy <= 243)] = 2  # JJA (Jun 1 - Aug 31)
        season_ids[(doy >= 244) & (doy <= 334)] = 3  # SON (Sep 1 - Nov 30)

        # Early/late season masks
        early_mask = doy <= 182  # First half of year
        late_mask = doy > 182   # Second half of year
        
        # Convert precip to numpy for faster operations
        precip_np = precip_clean.values
        
        # Precompute unique years
        unique_years = np.unique(year_ids)
        
        logger.info(f"Preprocessed time indices: {len(valid_indices)} valid days, {len(unique_years)} years")
        
        return (precip_np, year_ids, season_ids, doy, early_mask, late_mask, unique_years)
    
    def compute_annual_maxima(self, precip_np, year_ids, unique_years):
        """
        Compute annual maximum precipitation for all years.
        
        Parameters:
        -----------
        precip_np : numpy.ndarray
            Precipitation data array
        year_ids : numpy.ndarray
            Year ID for each time step
        unique_years : numpy.ndarray
            Array of unique years
            
        Returns:
        --------
        numpy.ndarray
            Annual maximum precipitation
        """
        ny, nx = precip_np.shape[1], precip_np.shape[2]
        n_years = len(unique_years)
        annual_max_all = np.full((n_years, ny, nx), np.nan)
        
        for i, year in enumerate(unique_years):
            year_mask = year_ids == year
            annual_max_all[i] = np.nanmax(precip_np[year_mask], axis=0)
            
        return annual_max_all

    def compute_atmospheric_statistics(self, cluster_filtered, combined_mask):
        """
        Compute mean and anomaly statistics for atmospheric variables using chunked processing.

        Parameters:
        -----------
        cluster_filtered : numpy.ndarray
            Filtered cluster assignments
        combined_mask : numpy.ndarray
            Combined mask for valid data points

        Returns:
        --------
        dict
            Dictionary containing atmospheric statistics for each variable
        """
        if not self.has_atmos_data:
            return {}

        atmos_stats = {}

        for var_key, atmos_var in self.atmos_data.items():
            logger.info(f"Computing statistics for {var_key}")

            # Use chunked processing to avoid memory issues
            try:
                # Process atmospheric data in chunks to avoid loading everything into memory
                atmos_stats[var_key] = self._process_atmospheric_variable_chunked(
                    atmos_var, var_key, cluster_filtered, combined_mask
                )
            except Exception as e:
                logger.warning(f"Failed to process {var_key}: {e}")
                continue

        return atmos_stats

    def _process_atmospheric_variable_chunked(self, atmos_var, var_key, cluster_filtered, combined_mask):
        """
        Process a single atmospheric variable using chunked approach to manage memory.

        Parameters:
        -----------
        atmos_var : xarray.DataArray
            Atmospheric variable data
        var_key : str
            Variable key name
        cluster_filtered : numpy.ndarray
            Filtered cluster assignments
        combined_mask : numpy.ndarray
            Combined mask for valid data points

        Returns:
        --------
        dict
            Dictionary with processed atmospheric data
        """
        # Define chunk size for processing (adjust based on available memory)
        chunk_size = 1000  # Process 1000 time steps at a time

        # Get total time length we need
        total_time_needed = len(combined_mask)

        # Get spatial dimensions
        precip_shape = self.precip.shape[1:]  # Get spatial shape (ny, nx)

        logger.info(f"  Processing {var_key} in chunks of {chunk_size} time steps")

        # Initialize arrays to accumulate results
        ny, nx = precip_shape
        overall_sum = np.zeros((ny, nx), dtype=np.float64)
        overall_count = np.zeros((ny, nx), dtype=np.int64)

        # Process data in chunks to compute overall mean
        n_chunks = (total_time_needed + chunk_size - 1) // chunk_size

        for chunk_idx in range(n_chunks):
            start_idx = chunk_idx * chunk_size
            end_idx = min(start_idx + chunk_size, total_time_needed)

            # Get chunk of atmospheric data
            atmos_chunk = atmos_var.isel(time=slice(-total_time_needed + start_idx, -total_time_needed + end_idx))

            # Handle spatial interpolation if needed
            if atmos_chunk.shape[1:] != precip_shape:
                atmos_chunk = self._interpolate_atmospheric_chunk(atmos_chunk, var_key)
                if atmos_chunk is None:
                    continue

            # Convert to numpy and apply mask
            chunk_data = atmos_chunk.values
            chunk_mask = combined_mask[start_idx:end_idx]
            chunk_filtered = chunk_data[chunk_mask]

            # Accumulate for overall mean calculation
            if chunk_filtered.size > 0:
                with np.errstate(invalid='ignore'):
                    chunk_sum = np.nansum(chunk_filtered, axis=0)
                    chunk_count = np.sum(~np.isnan(chunk_filtered), axis=0)

                    overall_sum += np.nan_to_num(chunk_sum)
                    overall_count += chunk_count

        # Compute overall mean
        with np.errstate(invalid='ignore', divide='ignore'):
            overall_mean = np.divide(overall_sum, overall_count,
                                   out=np.full_like(overall_sum, np.nan),
                                   where=(overall_count > 0))

        logger.info(f"  Computed overall mean for {var_key}")

        # Now compute filtered data and anomalies in chunks
        filtered_data_list = []
        anomalies_list = []

        for chunk_idx in range(n_chunks):
            start_idx = chunk_idx * chunk_size
            end_idx = min(start_idx + chunk_size, total_time_needed)

            # Get chunk of atmospheric data
            atmos_chunk = atmos_var.isel(time=slice(-total_time_needed + start_idx, -total_time_needed + end_idx))

            # Handle spatial interpolation if needed
            if atmos_chunk.shape[1:] != precip_shape:
                atmos_chunk = self._interpolate_atmospheric_chunk(atmos_chunk, var_key)
                if atmos_chunk is None:
                    continue

            # Convert to numpy and apply mask
            chunk_data = atmos_chunk.values
            chunk_mask = combined_mask[start_idx:end_idx]
            chunk_filtered = chunk_data[chunk_mask]

            if chunk_filtered.size > 0:
                # Compute anomalies for this chunk
                chunk_anomalies = chunk_filtered - overall_mean[np.newaxis, :, :]

                filtered_data_list.append(chunk_filtered)
                anomalies_list.append(chunk_anomalies)

        # Concatenate all chunks
        if filtered_data_list:
            atmos_filtered = np.concatenate(filtered_data_list, axis=0)
            anomalies = np.concatenate(anomalies_list, axis=0)
        else:
            # No valid data
            atmos_filtered = np.array([]).reshape(0, ny, nx)
            anomalies = np.array([]).reshape(0, ny, nx)

        logger.info(f"  Completed chunked processing for {var_key}: {atmos_filtered.shape}")

        return {
            'data': atmos_filtered,
            'overall_mean': overall_mean,
            'anomalies': anomalies
        }

    def _interpolate_atmospheric_chunk(self, atmos_chunk, var_key):
        """
        Interpolate a chunk of atmospheric data to precipitation grid.

        Parameters:
        -----------
        atmos_chunk : xarray.DataArray
            Chunk of atmospheric data
        var_key : str
            Variable key for logging

        Returns:
        --------
        xarray.DataArray or None
            Interpolated chunk or None if interpolation fails
        """
        try:
            precip_shape = self.precip.shape[1:]

            # Get coordinate names from precipitation data
            precip_spatial_dims = [d for d in self.precip.dims if d != "time"]

            # Get coordinate names from atmospheric data (excluding time)
            atmos_spatial_dims = [d for d in atmos_chunk.dims if d != "time"]

            # Create interpolation mapping
            interp_dict = {}

            # Common coordinate name mappings
            coord_mappings = {
                'latitude': ['lat', 'y', 'latitude'],
                'longitude': ['lon', 'x', 'longitude'],
                'lat': ['latitude', 'y', 'lat'],
                'lon': ['longitude', 'x', 'lon'],
                'y': ['latitude', 'lat', 'y'],
                'x': ['longitude', 'lon', 'x']
            }

            # Try to match coordinates
            for precip_dim in precip_spatial_dims:
                for atmos_dim in atmos_spatial_dims:
                    # Direct match
                    if precip_dim == atmos_dim:
                        interp_dict[atmos_dim] = self.precip[precip_dim]
                        break
                    # Check mappings
                    elif precip_dim in coord_mappings and atmos_dim in coord_mappings[precip_dim]:
                        interp_dict[atmos_dim] = self.precip[precip_dim]
                        break
                    elif atmos_dim in coord_mappings and precip_dim in coord_mappings[atmos_dim]:
                        interp_dict[atmos_dim] = self.precip[precip_dim]
                        break

            if len(interp_dict) == len(atmos_spatial_dims):
                # Interpolate atmospheric data to precipitation grid
                atmos_interp = atmos_chunk.interp(interp_dict, method='linear')
                return atmos_interp
            else:
                logger.warning(f"  Could not match coordinates for {var_key} chunk")
                return None

        except Exception as e:
            logger.warning(f"  Failed to interpolate {var_key} chunk: {e}")
            return None

    def process_clusters(self):
        """
        Process all cluster counts and compute precipitation statistics.
        """
        # Preprocess time indices
        precip_np, year_ids, season_ids, _, _, _, unique_years = self.preprocess_time_indices()

        # Get spatial dimensions
        ny, nx = precip_np.shape[1], precip_np.shape[2]
        n_years = len(unique_years)

        # Compute annual maxima for all data
        annual_max_all = self.compute_annual_maxima(precip_np, year_ids, unique_years)

        # Loop over k
        for k in range(2, self.max_k + 1):
            logger.info(f"Processing k = {k}")
            varname = f"k={k}"
            if varname not in self.cluster_ds.variables:
                logger.warning(f"Variable '{varname}' not found in cluster file.")
                continue

            # Initialize arrays for this k value only (memory efficient)
            self.results['mean_annual_max'][k] = np.full((k, ny, nx), np.nan)
            self.results['mean_annual_total'][k] = np.full((k, ny, nx), np.nan)
            self.results['mean_annual_precip'][k] = np.full((k, ny, nx), np.nan)
            self.results['mean_all_days'][k] = np.full((k, ny, nx), np.nan)
            self.results['mean_seasonal'][k] = np.full((k, 4, ny, nx), np.nan)
            self.results['mean_seasonal_total'][k] = np.full((k, 4, ny, nx), np.nan)
            self.results['mean_seasonal_max'][k] = np.full((k, 4, ny, nx), np.nan)
            self.results['wt_frac'][k] = np.full((k, ny, nx), np.nan)
            self.results['wt_AMS_frac'][k] = np.full((k, ny, nx), np.nan)
            self.results['wt_freq_gridpoint'][k] = np.full((k, ny, nx), np.nan)
            self.results['wt_freq_ams_gridpoint'][k] = np.full((k, ny, nx), np.nan)

            # Initialize atmospheric arrays if data is available
            if self.has_atmos_data:
                for var_key in self.atmos_data.keys():
                    self.results[f'mean_{var_key}'][k] = np.full((k, ny, nx), np.nan)
                    self.results[f'anomaly_{var_key}'][k] = np.full((k, ny, nx), np.nan)

            cluster_all = self.cluster_ds[varname].values

            # Align cluster values to precip (last N days only)
            if cluster_all.shape[0] != len(precip_np):
                cluster_all = cluster_all[-len(precip_np):]

            # Convert to integers and handle any NaN values
            nan_mask = np.isnan(cluster_all)
            cluster_all_clean = np.round(cluster_all[~nan_mask]).astype(int)

            # Check for valid cluster range (should be 0 to k-1)
            valid_cluster_mask = (cluster_all_clean >= 0) & (cluster_all_clean < k)

            # Apply both NaN and range filters to all arrays
            combined_mask = ~nan_mask  # Start with non-NaN mask
            combined_mask[combined_mask] = valid_cluster_mask  # Apply range filter to remaining valid indices

            # Filter all arrays consistently
            cluster_filtered = cluster_all_clean[valid_cluster_mask]
            year_ids_filtered = year_ids[combined_mask]
            season_ids_filtered = season_ids[combined_mask]
            precip_np_filtered = precip_np[combined_mask]

            # Calculate cluster frequencies
            cluster_counts = np.bincount(cluster_filtered, minlength=k)
            cluster_freqs = cluster_counts / len(cluster_filtered)

            # Compute atmospheric statistics if data is available
            atmos_stats = self.compute_atmospheric_statistics(cluster_filtered, combined_mask)

            # Pre-calculate AMS information for all years and gridpoints (fully vectorized)
            logger.info(f"  Pre-calculating AMS data for k={k} ({n_years} years, {ny}x{nx} grid)")
            ams_clusters = np.full((n_years, ny, nx), -1, dtype=int)  # -1 indicates no valid AMS

            # Progress tracking
            years_processed = 0
            years_with_data = 0

            # Vectorized approach: process all years at once
            for i, year in enumerate(unique_years):
                year_mask = year_ids_filtered == year
                if not np.any(year_mask):
                    continue

                year_precip = precip_np_filtered[year_mask]
                year_clusters = cluster_filtered[year_mask]

                if year_precip.shape[0] == 0:
                    continue

                years_processed += 1

                try:
                    # Safe vectorized approach: handle all-NaN gridpoints
                    with np.errstate(invalid='ignore'):
                        # Create valid mask first (gridpoints with at least one non-NaN value)
                        valid_mask = ~np.all(np.isnan(year_precip), axis=0)  # Shape: (ny, nx)

                        if np.any(valid_mask):
                            # Replace NaN values with a very small number for argmax calculation
                            # This allows np.argmax to work without errors
                            year_precip_safe = np.where(np.isnan(year_precip), -9999, year_precip)

                            # Find max indices for all gridpoints at once (now safe)
                            max_indices = np.argmax(year_precip_safe, axis=0)  # Shape: (ny, nx)

                            # Get the cluster for each gridpoint's maximum day
                            max_day_clusters = year_clusters[max_indices]  # Shape: (ny, nx)

                            # Only assign where we have valid data (not all-NaN)
                            ams_clusters[i] = np.where(valid_mask, max_day_clusters, -1)
                            years_with_data += 1
                        else:
                            # No valid data for this year
                            ams_clusters[i] = -1

                except Exception as e:
                    logger.warning(f"    Error processing year {year}: {e}")
                    ams_clusters[i] = -1

                # Progress reporting for large datasets
                if years_processed % 10 == 0 or years_processed == n_years:
                    logger.info(f"    Processed {years_processed}/{n_years} years ({years_with_data} with valid data)")

            logger.info(f"  AMS pre-calculation complete: {years_with_data}/{n_years} years with valid data")
            
            # Vectorized calculations for all clusters at once
            logger.info(f"  Computing statistics for all {k} clusters (vectorized)")

            # Pre-compute cluster masks for all clusters
            cluster_masks = [(cluster_filtered == c) for c in range(k)]
            cluster_day_counts = [np.sum(mask) for mask in cluster_masks]

            # Log cluster sizes
            for c, n_days in enumerate(cluster_day_counts):
                logger.info(f"    Cluster {c}: {n_days} days")

            # Vectorized frequency calculations (same for all gridpoints)
            for c in range(k):
                self.results['wt_frac'][k][c, :, :] = cluster_freqs[c]
                self.results['wt_freq_gridpoint'][k][c, :, :] = cluster_freqs[c]

            # Vectorized mean calculations
            for c in range(k):
                if cluster_day_counts[c] > 0:
                    cluster_precip = precip_np_filtered[cluster_masks[c]]
                    self.results['mean_all_days'][k][c, :, :] = np.nanmean(cluster_precip, axis=0)
                else:
                    self.results['mean_all_days'][k][c, :, :] = np.nan

            # Vectorized atmospheric calculations
            if self.has_atmos_data and atmos_stats:
                logger.info(f"    Computing atmospheric statistics for all {k} clusters")
                for var_key, var_stats in atmos_stats.items():
                    atmos_data_filtered = var_stats['data']
                    atmos_anomalies = var_stats['anomalies']

                    for c in range(k):
                        if cluster_day_counts[c] > 0:
                            cluster_mask = cluster_masks[c]
                            # Mean atmospheric variable for this cluster
                            cluster_atmos = atmos_data_filtered[cluster_mask]

                            # Check if we have valid data after masking
                            if cluster_atmos.size > 0:
                                with np.errstate(invalid='ignore'):
                                    self.results[f'mean_{var_key}'][k][c, :, :] = np.nanmean(cluster_atmos, axis=0)

                                # Mean anomaly for this cluster
                                cluster_anomalies = atmos_anomalies[cluster_mask]
                                with np.errstate(invalid='ignore'):
                                    self.results[f'anomaly_{var_key}'][k][c, :, :] = np.nanmean(cluster_anomalies, axis=0)
                            else:
                                logger.warning(f"    No valid atmospheric data for {var_key}, cluster {c}")
                                self.results[f'mean_{var_key}'][k][c, :, :] = np.nan
                                self.results[f'anomaly_{var_key}'][k][c, :, :] = np.nan
                        else:
                            self.results[f'mean_{var_key}'][k][c, :, :] = np.nan
                            self.results[f'anomaly_{var_key}'][k][c, :, :] = np.nan

            # Vectorized annual calculations for all clusters
            logger.info(f"    Computing annual statistics (vectorized)")

            # Pre-allocate arrays for all clusters
            all_annual_sums = np.full((k, n_years, ny, nx), np.nan)
            all_annual_maxes = np.full((k, n_years, ny, nx), np.nan)

            # Process each year once for all clusters
            for i, year in enumerate(unique_years):
                year_mask = year_ids_filtered == year
                if not np.any(year_mask):
                    continue

                year_precip = precip_np_filtered[year_mask]
                year_clusters = cluster_filtered[year_mask]

                # For each cluster, compute annual stats for this year
                for c in range(k):
                    cluster_year_mask = year_clusters == c
                    if np.any(cluster_year_mask):
                        cluster_year_precip = year_precip[cluster_year_mask]
                        all_annual_sums[c, i] = np.nansum(cluster_year_precip, axis=0)
                        all_annual_maxes[c, i] = np.nanmax(cluster_year_precip, axis=0)

            # Store results for all clusters
            for c in range(k):
                self.results['mean_annual_total'][k][c, :, :] = np.nanmean(all_annual_sums[c], axis=0)
                self.results['mean_annual_max'][k][c, :, :] = np.nanmean(all_annual_maxes[c], axis=0)

                # Compute mean annual precipitation (mean over all years of yearly mean precipitation)
                # First compute annual mean precipitation for each year
                annual_means = np.full((n_years, ny, nx), np.nan)
                for i, year in enumerate(unique_years):
                    year_mask = year_ids_filtered == year
                    if np.any(year_mask):
                        year_precip = precip_np_filtered[year_mask]
                        year_clusters = cluster_filtered[year_mask]

                        cluster_year_mask = year_clusters == c
                        if np.any(cluster_year_mask):
                            cluster_year_precip = year_precip[cluster_year_mask]
                            # Mean precipitation for this cluster in this year
                            annual_means[i] = np.nanmean(cluster_year_precip, axis=0)

                # Mean over all years of the yearly mean precipitation
                self.results['mean_annual_precip'][k][c, :, :] = np.nanmean(annual_means, axis=0)

            # Vectorized AMS calculations for all clusters at once
            logger.info(f"    Computing AMS statistics (vectorized)")

            # Pre-compute for all clusters
            valid_years_mask = (ams_clusters >= 0)  # Shape: (n_years, ny, nx)
            valid_year_count = np.sum(valid_years_mask, axis=0)  # Shape: (ny, nx)

            for c in range(k):
                # AMS fraction calculation
                wt_produces_ams = (ams_clusters == c)  # Shape: (n_years, ny, nx)
                ams_matches = np.sum(wt_produces_ams, axis=0)  # Shape: (ny, nx)

                self.results['wt_AMS_frac'][k][c, :, :] = np.divide(
                    ams_matches, valid_year_count,
                    out=np.full_like(ams_matches, np.nan, dtype=float),
                    where=(valid_year_count > 0)
                )

                # AMS frequency calculation
                self.results['wt_freq_ams_gridpoint'][k][c, :, :] = np.divide(
                    ams_matches, valid_year_count,
                    out=np.full_like(ams_matches, np.nan, dtype=float),
                    where=(valid_year_count > 0)
                )
                
            # Vectorized seasonal calculations for all clusters
            logger.info(f"    Computing seasonal statistics (vectorized)")

            # Process seasonal data for all clusters at once
            for c in range(k):
                if cluster_day_counts[c] == 0:
                    # No data for this cluster
                    for season in range(4):
                        self.results['mean_seasonal'][k][c, season, :, :] = np.nan
                        self.results['mean_seasonal_total'][k][c, season, :, :] = np.nan
                        self.results['mean_seasonal_max'][k][c, season, :, :] = np.nan
                    continue

                cluster_mask = cluster_masks[c]
                cluster_precip = precip_np_filtered[cluster_mask]
                cluster_year_ids = year_ids_filtered[cluster_mask]
                cluster_season_ids = season_ids_filtered[cluster_mask]

                # Process each season
                for season in range(4):
                    season_mask = cluster_season_ids == season
                    if not np.any(season_mask):
                        self.results['mean_seasonal'][k][c, season, :, :] = np.nan
                        self.results['mean_seasonal_total'][k][c, season, :, :] = np.nan
                        self.results['mean_seasonal_max'][k][c, season, :, :] = np.nan
                        continue

                    season_precip = cluster_precip[season_mask]
                    self.results['mean_seasonal'][k][c, season, :, :] = np.nanmean(season_precip, axis=0)

                    # Seasonal annual statistics (vectorized)
                    season_year_ids = cluster_year_ids[season_mask]
                    season_annual_sums = np.full((n_years, ny, nx), np.nan)
                    season_annual_maxes = np.full((n_years, ny, nx), np.nan)

                    # Vectorized year processing
                    for i, year in enumerate(unique_years):
                        year_season_mask = season_year_ids == year
                        if np.any(year_season_mask):
                            year_season_data = season_precip[year_season_mask]
                            season_annual_sums[i] = np.nansum(year_season_data, axis=0)
                            season_annual_maxes[i] = np.nanmax(year_season_data, axis=0)

                    self.results['mean_seasonal_total'][k][c, season, :, :] = np.nanmean(season_annual_sums, axis=0)
                    self.results['mean_seasonal_max'][k][c, season, :, :] = np.nanmean(season_annual_maxes, axis=0)
        
        logger.info("Finished processing all clusters")
    
    def save_results(self):
        """
        Save results to NetCDF file.
        """
        # Get spatial dimensions
        spatial_dim1, spatial_dim2 = self.spatial_dim1, self.spatial_dim2

        # Convert dictionary results to full arrays for saving
        # Find the maximum k value that was actually processed
        max_k_processed = max(self.results['mean_annual_max'].keys()) if self.results['mean_annual_max'] else 2

        # Initialize full arrays
        ny, nx = self.ny, self.nx
        mean_annual_max = np.full((max_k_processed + 1, max_k_processed, ny, nx), np.nan)
        mean_annual_total = np.full((max_k_processed + 1, max_k_processed, ny, nx), np.nan)
        mean_annual_precip = np.full((max_k_processed + 1, max_k_processed, ny, nx), np.nan)
        mean_all_days = np.full((max_k_processed + 1, max_k_processed, ny, nx), np.nan)
        mean_seasonal = np.full((max_k_processed + 1, max_k_processed, 4, ny, nx), np.nan)
        mean_seasonal_total = np.full((max_k_processed + 1, max_k_processed, 4, ny, nx), np.nan)
        mean_seasonal_max = np.full((max_k_processed + 1, max_k_processed, 4, ny, nx), np.nan)
        wt_frac = np.full((max_k_processed + 1, max_k_processed, ny, nx), np.nan)
        wt_AMS_frac = np.full((max_k_processed + 1, max_k_processed, ny, nx), np.nan)
        wt_freq_gridpoint = np.full((max_k_processed + 1, max_k_processed, ny, nx), np.nan)
        wt_freq_ams_gridpoint = np.full((max_k_processed + 1, max_k_processed, ny, nx), np.nan)

        # Initialize atmospheric arrays if data is available
        atmos_arrays = {}
        if self.has_atmos_data:
            for var_key in self.atmos_data.keys():
                atmos_arrays[f'mean_{var_key}'] = np.full((max_k_processed + 1, max_k_processed, ny, nx), np.nan)
                atmos_arrays[f'anomaly_{var_key}'] = np.full((max_k_processed + 1, max_k_processed, ny, nx), np.nan)

        # Fill arrays with computed results
        for var_name, var_dict in self.results.items():
            for k, k_data in var_dict.items():
                if var_name == 'mean_annual_max':
                    mean_annual_max[k, :k] = k_data
                elif var_name == 'mean_annual_total':
                    mean_annual_total[k, :k] = k_data
                elif var_name == 'mean_annual_precip':
                    mean_annual_precip[k, :k] = k_data
                elif var_name == 'mean_all_days':
                    mean_all_days[k, :k] = k_data
                elif var_name == 'mean_seasonal':
                    mean_seasonal[k, :k] = k_data
                elif var_name == 'mean_seasonal_total':
                    mean_seasonal_total[k, :k] = k_data
                elif var_name == 'mean_seasonal_max':
                    mean_seasonal_max[k, :k] = k_data
                elif var_name == 'wt_frac':
                    wt_frac[k, :k] = k_data
                elif var_name == 'wt_AMS_frac':
                    wt_AMS_frac[k, :k] = k_data
                elif var_name == 'wt_freq_gridpoint':
                    wt_freq_gridpoint[k, :k] = k_data
                elif var_name == 'wt_freq_ams_gridpoint':
                    wt_freq_ams_gridpoint[k, :k] = k_data
                elif var_name in atmos_arrays:
                    # Fill atmospheric arrays
                    atmos_arrays[var_name][k, :k] = k_data

        # Create dataset - start with base variables
        data_vars = {
            "mean_annual_max": (["k", "cluster", spatial_dim1, spatial_dim2], mean_annual_max),
            "mean_annual_total": (["k", "cluster", spatial_dim1, spatial_dim2], mean_annual_total),
            "mean_annual_precip": (["k", "cluster", spatial_dim1, spatial_dim2], mean_annual_precip),
            "mean_all_days": (["k", "cluster", spatial_dim1, spatial_dim2], mean_all_days),
            "mean_seasonal": (["k", "cluster", "season", spatial_dim1, spatial_dim2], mean_seasonal),
            "mean_seasonal_total": (["k", "cluster", "season", spatial_dim1, spatial_dim2], mean_seasonal_total),
            "mean_seasonal_max": (["k", "cluster", "season", spatial_dim1, spatial_dim2], mean_seasonal_max),
            "wt_frac": (["k", "cluster", spatial_dim1, spatial_dim2], wt_frac),
            "wt_AMS_frac": (["k", "cluster", spatial_dim1, spatial_dim2], wt_AMS_frac),
            "wt_freq_gridpoint": (["k", "cluster", spatial_dim1, spatial_dim2], wt_freq_gridpoint),
            "wt_freq_ams_gridpoint": (["k", "cluster", spatial_dim1, spatial_dim2], wt_freq_ams_gridpoint),
        }

        # Add atmospheric variables if available
        if self.has_atmos_data:
            for var_name, var_array in atmos_arrays.items():
                data_vars[var_name] = (["k", "cluster", spatial_dim1, spatial_dim2], var_array)

        output_ds = xr.Dataset(
            data_vars,
            coords={
                "k": np.arange(max_k_processed + 1),
                "cluster": np.arange(max_k_processed),
                "season": np.array(["DJF", "MAM", "JJA", "SON"]),  # Season labels
                spatial_dim1: self.precip[spatial_dim1],
                spatial_dim2: self.precip[spatial_dim2],
            },
        )

        # Add variable attributes for better documentation
        output_ds["wt_freq_gridpoint"].attrs = {
            "long_name": "Frequency of each weather type at each gridpoint",
            "description": "Fraction of days that each weather type occurs (same for all gridpoints since WT is spatially uniform)",
            "units": "fraction"
        }

        output_ds["wt_freq_ams_gridpoint"].attrs = {
            "long_name": "Frequency of each weather type when annual maximum precipitation occurs at each gridpoint",
            "description": "Fraction of annual maximum precipitation days that occur under each weather type at each gridpoint",
            "units": "fraction"
        }

        output_ds["wt_frac"].attrs = {
            "long_name": "Weather type fraction (legacy)",
            "description": "Overall fraction of days for each weather type",
            "units": "fraction"
        }

        output_ds["wt_AMS_frac"].attrs = {
            "long_name": "Annual maximum series fraction (legacy)",
            "description": "Fraction of years where this weather type produces the annual maximum",
            "units": "fraction"
        }

        output_ds["mean_annual_precip"].attrs = {
            "long_name": "Mean annual precipitation for each weather type",
            "description": "Mean over all years of the yearly mean precipitation at each grid point for each weather type",
            "units": "mm/day"
        }

        # Add attributes for atmospheric variables if available
        if self.has_atmos_data:
            # Define units and descriptions for atmospheric variables
            atmos_attrs = {
                'h500': {'units': 'm', 'long_name': '500 hPa geopotential height'},
                'mslp': {'units': 'Pa', 'long_name': 'Mean sea level pressure'},
                'u850': {'units': 'm/s', 'long_name': '850 hPa zonal wind component'},
                'v850': {'units': 'm/s', 'long_name': '850 hPa meridional wind component'}
            }

            for var_key in self.atmos_data.keys():
                if var_key in atmos_attrs:
                    attrs = atmos_attrs[var_key]

                    # Mean variable attributes
                    if f'mean_{var_key}' in output_ds.data_vars:
                        output_ds[f'mean_{var_key}'].attrs = {
                            "long_name": f"Mean {attrs['long_name']} for each weather type",
                            "description": f"Average {attrs['long_name']} for all days assigned to each weather type",
                            "units": attrs['units']
                        }

                    # Anomaly variable attributes
                    if f'anomaly_{var_key}' in output_ds.data_vars:
                        output_ds[f'anomaly_{var_key}'].attrs = {
                            "long_name": f"Mean {attrs['long_name']} anomaly for each weather type",
                            "description": f"Average anomaly (deviation from overall mean) of {attrs['long_name']} for each weather type",
                            "units": attrs['units']
                        }

        output_ds.to_netcdf(self.output_path)
        logger.info(f"Saved results to {self.output_path}")
    
    def run(self):
        """
        Run the full precipitation analysis pipeline.
        """
        logger.info("Starting precipitation analysis")
        self.process_clusters()
        self.save_results()
        logger.info("Precipitation analysis complete")
