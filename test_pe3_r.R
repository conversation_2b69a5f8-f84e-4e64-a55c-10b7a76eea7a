
# PE3 focused test
library(lmomco)

# Data
regional_tr <- 0.110298
regional_tr3 <- 0.027859

# Create L-moments
l1 <- 1.0
l2 <- regional_tr * l1
lmoms <- vec2lmom(c(l1, l2, regional_tr3, 0.0))

cat("R lmomco PE3 Analysis\n")
cat("====================\n")
cat("Regional TR:", sprintf("%.6f", regional_tr), "\n")
cat("Regional TR3:", sprintf("%.6f", regional_tr3), "\n")
cat("Expected gamma from book: 0.1626\n\n")

# Fit PE3
pe3_params <- lmom2par(lmoms, type="pe3")

if (!is.null(pe3_params) && !is.null(pe3_params$para)) {
  cat("R PE3 parameters:\n")
  cat("  mu:", sprintf("%.6f", pe3_params$para[1]), "\n")
  cat("  sigma:", sprintf("%.6f", pe3_params$para[2]), "\n")
  cat("  gamma:", sprintf("%.6f", pe3_params$para[3]), "\n")
  
  # Check if gamma matches expected value
  expected_gamma <- 0.1626
  actual_gamma <- pe3_params$para[3]
  difference <- abs(actual_gamma - expected_gamma)
  
  cat("\nComparison with expected:\n")
  cat("  Expected gamma: 0.1626\n")
  cat("  Actual gamma:", sprintf("%.6f", actual_gamma), "\n")
  cat("  Difference:", sprintf("%.6f", difference), "\n")
  
  if (difference < 0.001) {
    cat("  Close match!\n")
  } else {
    cat("  Significant difference\n")
  }
} else {
  cat("Error fitting PE3 distribution\n")
}
