#!/usr/bin/env python3
"""
Detailed PE3 distribution testing to investigate gamma parameter discrepancy.
"""

import numpy as np
from lmoments3 import distr
import subprocess
import os

def test_pe3_detailed():
    """Test PE3 distribution in detail"""
    
    print("PE3 DISTRIBUTION DETAILED ANALYSIS")
    print("=" * 50)
    
    # Your data
    tr_values = [.1209, .0915, .1124, .1032, .0967, .1328, .1008, .1143, .1107, .1179, 
                 .1308, .1119, .1018, .1025, .1054, .1174, .1115, .1003, .1046]
    
    tr3_values = [.0488, .0105, .0614, .0417, -.0134, -.0176, .0943, .0555, .0478, .0492, 
                  .0940, -.0429, .0435, .0182, -.0224, .0124, -.0346, .0446, .0128]
    
    rec_length = [98, 59, 90, 61, 65, 86, 78, 72, 67, 99, 49, 61, 69, 73, 70, 66, 59, 74, 82]
    
    # Calculate weighted regional L-moment ratios
    tr_array = np.array(tr_values)
    tr3_array = np.array(tr3_values)
    rec_array = np.array(rec_length)
    
    regional_tr = (tr_array * rec_array).sum() / rec_array.sum()
    regional_tr3 = (tr3_array * rec_array).sum() / rec_array.sum()
    
    print(f"Regional TR: {regional_tr:.6f}")
    print(f"Regional TR3: {regional_tr3:.6f}")
    print(f"Expected gamma from book: 0.1626")
    
    # Test PE3 with lmoments3
    print(f"\n{'='*50}")
    print("PYTHON lmoments3 PE3 ANALYSIS")
    print(f"{'='*50}")
    
    l1 = 1.0
    lmom_ratios = [l1, regional_tr * l1, regional_tr3, 0.0]
    
    print(f"L-moment ratios used for fitting:")
    print(f"  L1: {lmom_ratios[0]:.6f}")
    print(f"  L2: {lmom_ratios[1]:.6f}")
    print(f"  T3: {lmom_ratios[2]:.6f}")
    print(f"  T4: {lmom_ratios[3]:.6f}")
    
    try:
        pe3_params = distr.pe3.lmom_fit(lmom_ratios=lmom_ratios)
        print(f"\nPython PE3 parameters:")
        for key, value in pe3_params.items():
            print(f"  {key}: {value:.6f}")
        
        # Calculate theoretical L-moments back
        theo_lmoms = distr.pe3.lmom_ratios(**pe3_params)
        print(f"\nTheoretical L-moments from fitted parameters:")
        print(f"  L1: {theo_lmoms[0]:.6f}")
        print(f"  TR: {theo_lmoms[1]:.6f}")
        print(f"  T3: {theo_lmoms[2]:.6f}")
        print(f"  T4: {theo_lmoms[3]:.6f}")
        
        # Check fit quality
        tr_error = abs(theo_lmoms[1] - regional_tr)
        t3_error = abs(theo_lmoms[2] - regional_tr3)
        print(f"\nFit quality:")
        print(f"  TR error: {tr_error:.8f}")
        print(f"  T3 error: {t3_error:.8f}")
        
    except Exception as e:
        print(f"Error with Python PE3: {e}")
    
    # Test different L1 values to see if that affects gamma
    print(f"\n{'='*50}")
    print("TESTING DIFFERENT L1 VALUES")
    print(f"{'='*50}")
    
    for test_l1 in [1.0, 10.0, 100.0]:
        test_lmoms = [test_l1, regional_tr * test_l1, regional_tr3, 0.0]
        try:
            params = distr.pe3.lmom_fit(lmom_ratios=test_lmoms)
            print(f"\nL1 = {test_l1}: gamma = {params['skew']:.6f}")
        except Exception as e:
            print(f"L1 = {test_l1}: Error - {e}")
    
    # Test manual calculation using different formulas
    print(f"\n{'='*50}")
    print("MANUAL GAMMA CALCULATION METHODS")
    print(f"{'='*50}")
    
    # Method 1: Direct from T3 using standard PE3 relationship
    # For PE3: T3 = (2/π) * arctan(γ/2) where γ is the skewness coefficient
    import math
    
    # Solve for gamma: γ = 2 * tan(π * T3 / 2)
    gamma_method1 = 2 * math.tan(math.pi * regional_tr3 / 2)
    print(f"Method 1 (direct from T3): gamma = {gamma_method1:.6f}")
    
    # Method 2: Using the approximation γ ≈ T3 * π for small T3
    gamma_method2 = regional_tr3 * math.pi
    print(f"Method 2 (small T3 approx): gamma = {gamma_method2:.6f}")
    
    # Method 3: Check if there's a scaling factor
    expected_gamma = 0.1626
    scaling_factor = expected_gamma / pe3_params['skew']
    print(f"Method 3 (scaling factor): {scaling_factor:.6f}")
    print(f"  If applied: {pe3_params['skew'] * scaling_factor:.6f}")

def run_r_pe3_test():
    """Run a focused R test for PE3"""
    
    print(f"\n{'='*50}")
    print("R lmomco PE3 ANALYSIS")
    print(f"{'='*50}")
    
    # Create a simple R script for PE3 testing
    r_script = '''
# PE3 focused test
library(lmomco)

# Data
regional_tr <- 0.110298
regional_tr3 <- 0.027859

# Create L-moments
l1 <- 1.0
l2 <- regional_tr * l1
lmoms <- vec2lmom(c(l1, l2, regional_tr3, 0.0))

cat("R lmomco PE3 Analysis\\n")
cat("====================\\n")
cat("Regional TR:", sprintf("%.6f", regional_tr), "\\n")
cat("Regional TR3:", sprintf("%.6f", regional_tr3), "\\n")
cat("Expected gamma from book: 0.1626\\n\\n")

# Fit PE3
pe3_params <- lmom2par(lmoms, type="pe3")

if (!is.null(pe3_params) && !is.null(pe3_params$para)) {
  cat("R PE3 parameters:\\n")
  cat("  mu:", sprintf("%.6f", pe3_params$para[1]), "\\n")
  cat("  sigma:", sprintf("%.6f", pe3_params$para[2]), "\\n")
  cat("  gamma:", sprintf("%.6f", pe3_params$para[3]), "\\n")
  
  # Check if gamma matches expected value
  expected_gamma <- 0.1626
  actual_gamma <- pe3_params$para[3]
  difference <- abs(actual_gamma - expected_gamma)
  
  cat("\\nComparison with expected:\\n")
  cat("  Expected gamma: 0.1626\\n")
  cat("  Actual gamma:", sprintf("%.6f", actual_gamma), "\\n")
  cat("  Difference:", sprintf("%.6f", difference), "\\n")
  
  if (difference < 0.001) {
    cat("  Close match!\\n")
  } else {
    cat("  Significant difference\\n")
  }
} else {
  cat("Error fitting PE3 distribution\\n")
}
'''
    
    # Write and run the R script
    with open('test_pe3_r.R', 'w', encoding='utf-8') as f:
        f.write(r_script)
    
    try:
        # Try to find Rscript
        rscript_paths = [
            'Rscript',
            r'C:\Program Files\R\R-4.3.0\bin\x64\Rscript.exe',
            r'C:\Program Files\R\R-4.3.0\bin\Rscript.exe'
        ]
        
        result = None
        for rscript_path in rscript_paths:
            try:
                result = subprocess.run(
                    [rscript_path, 'test_pe3_r.R'], 
                    capture_output=True, 
                    text=True,
                    cwd=os.getcwd()
                )
                break
            except FileNotFoundError:
                continue
        
        if result and result.returncode == 0:
            print(result.stdout)
            if result.stderr:
                print("Warnings:")
                print(result.stderr)
        else:
            print("Could not run R script")
            if result:
                print("Error:", result.stderr)
                
    except Exception as e:
        print(f"Error running R script: {e}")

if __name__ == "__main__":
    test_pe3_detailed()
    run_r_pe3_test()
