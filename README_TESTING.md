# Testing Guide for Weather Typing and Precipitation Regionalization

## Overview

This guide provides comprehensive testing instructions for all components of the weather typing and precipitation regionalization analysis system. Tests are organized by functionality and include both unit tests and integration tests.

## Test Directory Structure

```
tests/
├── test_weather_typing.py           # Weather typing analysis tests
├── test_precipitation_stats.py      # Precipitation statistics tests
├── test_regionalization.py          # Precipitation regionalization tests
├── test_heterogeneity.py           # Heterogeneity analysis tests
├── test_clustering_methods.py       # Clustering method comparison tests
├── test_atmospheric_variables.py    # Atmospheric variable processing tests
├── test_mean_annual_precip.py      # Mean annual precipitation tests
├── test_visualization.py           # Visualization and plotting tests
├── test_memory_performance.py      # Memory and performance tests
├── test_grid_interpolation.py      # Grid interpolation tests
└── synthetic_data/                 # Synthetic test data
    ├── create_test_data.py         # Script to generate test datasets
    ├── test_precip.nc              # Synthetic precipitation data
    ├── test_atmos.nc               # Synthetic atmospheric data
    └── test_coords.nc              # Synthetic coordinate data
```

## Prerequisites for Testing

### Install Testing Dependencies
```bash
pip install pytest pytest-cov numpy pandas xarray netcdf4 matplotlib cartopy scikit-learn scipy
```

### Generate Synthetic Test Data
```bash
cd tests/synthetic_data
python create_test_data.py
```

## Running Tests

### Run All Tests
```bash
# From project root directory
pytest tests/ -v
```

### Run Tests with Coverage
```bash
pytest tests/ --cov=src --cov-report=html --cov-report=term
```

### Run Specific Test Categories

#### Weather Typing Tests
```bash
pytest tests/test_weather_typing.py -v
```

#### Precipitation Statistics Tests
```bash
pytest tests/test_precipitation_stats.py -v
```

#### Clustering Method Tests
```bash
pytest tests/test_clustering_methods.py -v
```

#### Memory and Performance Tests
```bash
pytest tests/test_memory_performance.py -v
```

## Individual Test Descriptions

### 1. Weather Typing Analysis Tests (`test_weather_typing.py`)

**Purpose**: Validate weather typing clustering and classifiability index calculations.

**Test Cases**:
- EOF analysis and dimensionality reduction
- K-means clustering with multiple k values
- Classifiability index calculation
- Output file format validation
- Coordinate system handling

**Run Test**:
```bash
pytest tests/test_weather_typing.py::test_wt_clustering -v
pytest tests/test_weather_typing.py::test_ci_calculation -v
```

### 2. Precipitation Statistics Tests (`test_precipitation_stats.py`)

**Purpose**: Validate precipitation statistics generation including atmospheric variables.

**Test Cases**:
- Precipitation statistics calculation for weather types
- Atmospheric variable mean and anomaly calculations
- **Mean annual precipitation calculation** (NEW)
- Weather type frequency calculations
- Grid interpolation functionality
- Chunked processing for large datasets

**Run Test**:
```bash
pytest tests/test_precipitation_stats.py::test_precip_stats -v
pytest tests/test_precipitation_stats.py::test_atmospheric_variables -v
pytest tests/test_precipitation_stats.py::test_mean_annual_precip -v
```

### 3. Mean Annual Precipitation Tests (`test_mean_annual_precip.py`)

**Purpose**: Specifically validate the new mean annual precipitation calculation.

**Test Cases**:
- Synthetic data with known annual patterns
- Multi-year averaging calculations
- Weather type-specific annual means
- Output format and metadata validation
- Edge cases (missing years, incomplete data)

**Run Test**:
```bash
python tests/test_mean_annual_precip.py
```

### 4. Regionalization Tests (`test_regionalization.py`)

**Purpose**: Validate precipitation regionalization with and without weather types.

**Test Cases**:
- K-means clustering regionalization
- Hierarchical clustering regionalization
- Method comparison functionality
- Output consistency between methods
- Weather type integration

**Run Test**:
```bash
pytest tests/test_regionalization.py::test_kmeans_regionalization -v
pytest tests/test_regionalization.py::test_hierarchical_regionalization -v
```

### 5. Clustering Method Tests (`test_clustering_methods.py`)

**Purpose**: Compare K-means and hierarchical clustering methods.

**Test Cases**:
- Method selection functionality
- Output format consistency
- Performance comparison
- Deterministic vs. stochastic results
- Dendrogram generation (hierarchical)

**Run Test**:
```bash
python tests/test_clustering_methods.py
```

### 6. Atmospheric Variable Tests (`test_atmospheric_variables.py`)

**Purpose**: Validate atmospheric variable processing and anomaly calculations.

**Test Cases**:
- H500, MSLP, U850, V850 data loading
- Mean calculation for each weather type
- Anomaly calculation (deviation from climatology)
- Grid interpolation for atmospheric data
- Time alignment with precipitation data

**Run Test**:
```bash
pytest tests/test_atmospheric_variables.py -v
```

### 7. Heterogeneity Analysis Tests (`test_heterogeneity.py`)

**Purpose**: Validate L-moment heterogeneity statistics calculations.

**Test Cases**:
- H1, H2, H3 statistic calculations
- L-moment ratio calculations
- Regional homogeneity assessment
- Monte Carlo simulation validation
- Output format verification

**Run Test**:
```bash
pytest tests/test_heterogeneity.py -v
```

### 8. Visualization Tests (`test_visualization.py`)

**Purpose**: Validate plotting and visualization functionality.

**Test Cases**:
- Weather typing precipitation maps
- Regional boundary plotting
- Coordinate system handling
- Color scheme consistency
- Output file generation

**Run Test**:
```bash
pytest tests/test_visualization.py -v
```

### 9. Memory and Performance Tests (`test_memory_performance.py`)

**Purpose**: Validate memory management and performance optimizations.

**Test Cases**:
- Chunked processing functionality
- Memory usage monitoring
- Large dataset handling
- Performance benchmarking
- Grid interpolation efficiency

**Run Test**:
```bash
pytest tests/test_memory_performance.py -v
```

### 10. Grid Interpolation Tests (`test_grid_interpolation.py`)

**Purpose**: Validate automatic grid interpolation functionality.

**Test Cases**:
- Different coordinate system handling
- Spatial resolution mismatch resolution
- Interpolation accuracy
- Coordinate name mapping
- Edge case handling

**Run Test**:
```bash
pytest tests/test_grid_interpolation.py -v
```

## Integration Tests

### Full Workflow Test
```bash
pytest tests/test_full_workflow.py -v
```

This test runs the complete analysis pipeline with synthetic data to ensure all components work together correctly.

### Performance Benchmark Test
```bash
pytest tests/test_performance_benchmark.py -v
```

This test measures performance metrics and ensures the system meets performance requirements.

## Test Data Management

### Synthetic Data Generation
The testing system uses synthetic data that mimics the structure and characteristics of real climate data:

- **Precipitation Data**: Synthetic daily precipitation with realistic patterns
- **Atmospheric Variables**: Synthetic h500, mslp, u850, v850 with correlated patterns
- **Coordinate Data**: Synthetic latitude/longitude grids matching real data structure

### Test Data Validation
All synthetic data includes:
- Known patterns for validation
- Controlled statistical properties
- Realistic spatial and temporal correlations
- Edge cases for robust testing

## Continuous Integration

### GitHub Actions Workflow
```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.8
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install pytest pytest-cov
      - name: Generate test data
        run: python tests/synthetic_data/create_test_data.py
      - name: Run tests
        run: pytest tests/ --cov=src --cov-report=xml
```

## Test Coverage Goals

- **Overall Coverage**: > 90%
- **Core Algorithms**: > 95%
- **Error Handling**: > 85%
- **Integration Points**: > 90%

## Troubleshooting Tests

### Common Test Issues

1. **Missing Test Data**:
   ```bash
   cd tests/synthetic_data
   python create_test_data.py
   ```

2. **Memory Errors in Tests**:
   - Use smaller synthetic datasets
   - Run tests individually rather than in batch

3. **Coordinate System Issues**:
   - Verify synthetic coordinate data matches expected format
   - Check coordinate variable naming conventions

4. **Performance Test Failures**:
   - Adjust performance thresholds for different hardware
   - Use appropriate test data sizes

### Running Tests in Debug Mode
```bash
pytest tests/ -v -s --tb=long
```

## Contributing New Tests

When adding new functionality:

1. **Create corresponding test file** in `tests/` directory
2. **Use synthetic data** for reproducible testing
3. **Include edge cases** and error conditions
4. **Validate outputs** against known expected results
5. **Test performance** for large dataset scenarios
6. **Update this README** with new test descriptions

## Test Reporting

### Generate HTML Coverage Report
```bash
pytest tests/ --cov=src --cov-report=html
open htmlcov/index.html
```

### Generate Test Summary
```bash
pytest tests/ --tb=short --quiet
```

This testing guide ensures comprehensive validation of all system components and provides confidence in the reliability and accuracy of the weather typing and precipitation regionalization analysis.
