#!/usr/bin/env python3
"""
Comprehensive comparison of L-moment distribution fitting across:
- Python lmoments3
- R lmomco package  
- R lmom package
"""

import numpy as np
from lmoments3 import distr
import subprocess
import os

def comprehensive_comparison():
    """Compare L-moment fitting across Python and R packages"""
    
    print("COMPREHENSIVE L-MOMENTS COMPARISON")
    print("=" * 60)
    print("Comparing: Python lmoments3, R lmomco, R lmom")
    print("=" * 60)
    
    # Your data
    tr_values = [.1209, .0915, .1124, .1032, .0967, .1328, .1008, .1143, .1107, .1179, 
                 .1308, .1119, .1018, .1025, .1054, .1174, .1115, .1003, .1046]
    
    tr3_values = [.0488, .0105, .0614, .0417, -.0134, -.0176, .0943, .0555, .0478, .0492, 
                  .0940, -.0429, .0435, .0182, -.0224, .0124, -.0346, .0446, .0128]
    
    rec_length = [98, 59, 90, 61, 65, 86, 78, 72, 67, 99, 49, 61, 69, 73, 70, 66, 59, 74, 82]
    
    # Calculate weighted regional L-moment ratios
    tr_array = np.array(tr_values)
    tr3_array = np.array(tr3_values)
    n_array = np.array(rec_length)
    
    regional_tr = (tr_array * n_array).sum() / n_array.sum()
    regional_tr3 = (tr3_array * n_array).sum() / n_array.sum()
    
    print(f"Regional L-moment ratios:")
    print(f"  λ₁ᴿ = 1.0000")
    print(f"  τ₂ᴿ = {regional_tr:.6f}")
    print(f"  τ₃ᴿ = {regional_tr3:.6f}")
    
    # Book values for comparison
    book_tau2 = 0.1103
    book_tau3 = 0.0279
    book_tau4 = 0.1366
    book_gamma = 0.1626
    
    print(f"\nBook values:")
    print(f"  τ₂ᴿ = {book_tau2:.4f}")
    print(f"  τ₃ᴿ = {book_tau3:.4f}")
    print(f"  τ₄ᴿ = {book_tau4:.4f}")
    print(f"  Expected PE3 γ = {book_gamma:.4f}")
    
    # Python lmoments3 analysis
    print(f"\n{'='*60}")
    print("PYTHON lmoments3 RESULTS")
    print(f"{'='*60}")
    
    l1 = 1.0
    lmom_ratios = [l1, regional_tr * l1, regional_tr3, 0.0]
    lmom_ratios_book = [l1, book_tau2, book_tau3, 0.0]
    lmom_ratios_book_t4 = [l1, book_tau2, book_tau3, book_tau4]
    
    python_results = {}
    distributions = {
        'KAPPA': distr.kap,
        'GEV': distr.gev,
        'GLO': distr.glo,
        'GPA': distr.gpa,
        'PE3': distr.pe3,
        'GNO': distr.gno
    }
    
    print(f"\nUsing our calculated values:")
    for name, dist in distributions.items():
        try:
            params = dist.lmom_fit(lmom_ratios=lmom_ratios)
            python_results[name] = params
            print(f"\n{name}:")
            for key, value in params.items():
                print(f"  {key}: {value:.6f}")
        except Exception as e:
            print(f"\n{name}: Error - {e}")
    
    print(f"\nUsing book values (τ₂={book_tau2}, τ₃={book_tau3}):")
    for name, dist in distributions.items():
        try:
            params = dist.lmom_fit(lmom_ratios=lmom_ratios_book)
            print(f"\n{name}:")
            for key, value in params.items():
                print(f"  {key}: {value:.6f}")
                if name == 'PE3' and key == 'skew':
                    print(f"    PE3 γ diff from book: {abs(value - book_gamma):.6f}")
        except Exception as e:
            print(f"\n{name}: Error - {e}")
    
    # R lmomco analysis
    print(f"\n{'='*60}")
    print("R lmomco ANALYSIS")
    print(f"{'='*60}")
    
    r_lmomco_script = f'''
library(lmomco)

# Data
regional_tr <- {regional_tr:.6f}
regional_tr3 <- {regional_tr3:.6f}
book_tau2 <- {book_tau2:.4f}
book_tau3 <- {book_tau3:.4f}
book_tau4 <- {book_tau4:.4f}
book_gamma <- {book_gamma:.4f}

cat("R lmomco Analysis\\n")
cat("=================\\n")

# Test with our calculated values
cat("\\nUsing our calculated values:\\n")
cat("τ₂ =", sprintf("%.6f", regional_tr), "\\n")
cat("τ₃ =", sprintf("%.6f", regional_tr3), "\\n")

lmoms_our <- vec2lmom(c(1.0, regional_tr, regional_tr3, 0.0))
distributions <- c("kap", "gev", "glo", "gpa", "pe3", "gno")

for (dist in distributions) {{
  cat("\\n", toupper(dist), ":\\n", sep="")
  tryCatch({{
    params <- lmom2par(lmoms_our, type=dist)
    if (!is.null(params) && !is.null(params$para)) {{
      for (i in 1:length(params$para)) {{
        cat("  param", i, ":", sprintf("%.6f", params$para[i]), "\\n")
      }}
      if (dist == "pe3") {{
        gamma_diff <- abs(params$para[3] - book_gamma)
        cat("  PE3 γ diff from book:", sprintf("%.6f", gamma_diff), "\\n")
      }}
    }}
  }}, error = function(e) {{
    cat("  Error:", e$message, "\\n")
  }})
}}

# Test with book values
cat("\\n\\nUsing book values:\\n")
cat("τ₂ =", sprintf("%.4f", book_tau2), "\\n")
cat("τ₃ =", sprintf("%.4f", book_tau3), "\\n")

lmoms_book <- vec2lmom(c(1.0, book_tau2, book_tau3, 0.0))

for (dist in distributions) {{
  cat("\\n", toupper(dist), ":\\n", sep="")
  tryCatch({{
    params <- lmom2par(lmoms_book, type=dist)
    if (!is.null(params) && !is.null(params$para)) {{
      for (i in 1:length(params$para)) {{
        cat("  param", i, ":", sprintf("%.6f", params$para[i]), "\\n")
      }}
      if (dist == "pe3") {{
        gamma_diff <- abs(params$para[3] - book_gamma)
        cat("  PE3 γ diff from book:", sprintf("%.6f", gamma_diff), "\\n")
      }}
    }}
  }}, error = function(e) {{
    cat("  Error:", e$message, "\\n")
  }})
}}

# Test with book values including T4
cat("\\n\\nUsing book values with T4:\\n")
cat("τ₂ =", sprintf("%.4f", book_tau2), "\\n")
cat("τ₃ =", sprintf("%.4f", book_tau3), "\\n")
cat("τ₄ =", sprintf("%.4f", book_tau4), "\\n")

lmoms_book_t4 <- vec2lmom(c(1.0, book_tau2, book_tau3, book_tau4))

cat("\\nKAPPA (4-parameter):\\n")
tryCatch({{
  kap_params <- lmom2par(lmoms_book_t4, type="kap")
  if (!is.null(kap_params) && !is.null(kap_params$para)) {{
    cat("  xi:", sprintf("%.6f", kap_params$para[1]), "\\n")
    cat("  alpha:", sprintf("%.6f", kap_params$para[2]), "\\n")
    cat("  kappa:", sprintf("%.6f", kap_params$para[3]), "\\n")
    cat("  h:", sprintf("%.6f", kap_params$para[4]), "\\n")
    
    # Check if kappa parameter matches book gamma
    kappa_diff <- abs(kap_params$para[3] - book_gamma)
    cat("  KAPPA κ diff from book γ:", sprintf("%.6f", kappa_diff), "\\n")
    if (kappa_diff < 0.01) {{
      cat("  *** KAPPA κ close to book PE3 γ! ***\\n")
    }}
  }}
}}, error = function(e) {{
  cat("  Error:", e$message, "\\n")
}})
'''
    
    # R lmom analysis
    print(f"\n{'='*60}")
    print("R lmom ANALYSIS")
    print(f"{'='*60}")
    
    r_lmom_script = f'''
library(lmom)

# Data
regional_tr <- {regional_tr:.6f}
regional_tr3 <- {regional_tr3:.6f}
book_tau2 <- {book_tau2:.4f}
book_tau3 <- {book_tau3:.4f}
book_tau4 <- {book_tau4:.4f}
book_gamma <- {book_gamma:.4f}

cat("R lmom Analysis\\n")
cat("===============\\n")

# Test with our calculated values
cat("\\nUsing our calculated values:\\n")
l1 <- 1.0
l2 <- regional_tr * l1
l3 <- regional_tr3 * l2
l4 <- 0.0

lmoms_our <- c(l1, l2, l3, l4)
cat("L-moments:", sprintf("%.6f", lmoms_our), "\\n")

# GEV
tryCatch({{
  gev_params <- pelgev(lmoms_our)
  cat("\\nGEV:\\n")
  cat("  xi:", sprintf("%.6f", gev_params[1]), "\\n")
  cat("  alpha:", sprintf("%.6f", gev_params[2]), "\\n")
  cat("  kappa:", sprintf("%.6f", gev_params[3]), "\\n")
}}, error = function(e) {{
  cat("\\nGEV: Error -", e$message, "\\n")
}})

# GLO
tryCatch({{
  glo_params <- pelglo(lmoms_our)
  cat("\\nGLO:\\n")
  cat("  xi:", sprintf("%.6f", glo_params[1]), "\\n")
  cat("  alpha:", sprintf("%.6f", glo_params[2]), "\\n")
  cat("  kappa:", sprintf("%.6f", glo_params[3]), "\\n")
}}, error = function(e) {{
  cat("\\nGLO: Error -", e$message, "\\n")
}})

# GPA
tryCatch({{
  gpa_params <- pelgpa(lmoms_our)
  cat("\\nGPA:\\n")
  cat("  xi:", sprintf("%.6f", gpa_params[1]), "\\n")
  cat("  alpha:", sprintf("%.6f", gpa_params[2]), "\\n")
  cat("  kappa:", sprintf("%.6f", gpa_params[3]), "\\n")
}}, error = function(e) {{
  cat("\\nGPA: Error -", e$message, "\\n")
}})

# PE3
tryCatch({{
  pe3_params <- pelpe3(lmoms_our)
  cat("\\nPE3:\\n")
  cat("  mu:", sprintf("%.6f", pe3_params[1]), "\\n")
  cat("  sigma:", sprintf("%.6f", pe3_params[2]), "\\n")
  cat("  gamma:", sprintf("%.6f", pe3_params[3]), "\\n")
  gamma_diff <- abs(pe3_params[3] - book_gamma)
  cat("  PE3 γ diff from book:", sprintf("%.6f", gamma_diff), "\\n")
}}, error = function(e) {{
  cat("\\nPE3: Error -", e$message, "\\n")
}})

# GNO
tryCatch({{
  gno_params <- pelgno(lmoms_our)
  cat("\\nGNO:\\n")
  cat("  xi:", sprintf("%.6f", gno_params[1]), "\\n")
  cat("  alpha:", sprintf("%.6f", gno_params[2]), "\\n")
  cat("  kappa:", sprintf("%.6f", gno_params[3]), "\\n")
}}, error = function(e) {{
  cat("\\nGNO: Error -", e$message, "\\n")
}})

# KAPPA (if available)
tryCatch({{
  kap_params <- pelkap(lmoms_our)
  cat("\\nKAPPA:\\n")
  cat("  xi:", sprintf("%.6f", kap_params[1]), "\\n")
  cat("  alpha:", sprintf("%.6f", kap_params[2]), "\\n")
  cat("  kappa:", sprintf("%.6f", kap_params[3]), "\\n")
  cat("  h:", sprintf("%.6f", kap_params[4]), "\\n")
}}, error = function(e) {{
  cat("\\nKAPPA: Error -", e$message, "\\n")
}})

# Test with book values
cat("\\n\\nUsing book values:\\n")
l1_book <- 1.0
l2_book <- book_tau2 * l1_book
l3_book <- book_tau3 * l2_book
l4_book <- 0.0

lmoms_book <- c(l1_book, l2_book, l3_book, l4_book)
cat("L-moments:", sprintf("%.6f", lmoms_book), "\\n")

# PE3 with book values
tryCatch({{
  pe3_book <- pelpe3(lmoms_book)
  cat("\\nPE3 (book values):\\n")
  cat("  mu:", sprintf("%.6f", pe3_book[1]), "\\n")
  cat("  sigma:", sprintf("%.6f", pe3_book[2]), "\\n")
  cat("  gamma:", sprintf("%.6f", pe3_book[3]), "\\n")
  gamma_diff <- abs(pe3_book[3] - book_gamma)
  cat("  PE3 γ diff from book:", sprintf("%.6f", gamma_diff), "\\n")
}}, error = function(e) {{
  cat("\\nPE3 (book): Error -", e$message, "\\n")
}})
'''
    
    # Run R scripts
    scripts = [
        ("lmomco", r_lmomco_script, "test_lmomco_comp.R"),
        ("lmom", r_lmom_script, "test_lmom_comp.R")
    ]
    
    for package_name, script_content, filename in scripts:
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            # Try to run R script
            rscript_paths = [
                'Rscript',
                r'C:\Program Files\R\R-4.3.0\bin\x64\Rscript.exe',
                r'C:\Program Files\R\R-4.3.0\bin\Rscript.exe'
            ]
            
            result = None
            for rscript_path in rscript_paths:
                try:
                    result = subprocess.run(
                        [rscript_path, filename], 
                        capture_output=True, 
                        text=True,
                        cwd=os.getcwd()
                    )
                    break
                except FileNotFoundError:
                    continue
            
            if result and result.returncode == 0:
                print(result.stdout)
                if result.stderr:
                    print("Warnings:")
                    print(result.stderr)
            else:
                print(f"Could not run R {package_name} script")
                
        except Exception as e:
            print(f"Error with R {package_name} script: {e}")
    
    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    
    print("Key findings:")
    print("• All three packages (Python lmoments3, R lmomco, R lmom) should give consistent results")
    print("• PE3 distribution gives γ ≈ 0.171 for both our calculated and book's τ₂, τ₃ values")
    print("• Book's expected γ = 0.1626 doesn't match PE3 calculations")
    print("• KAPPA distribution can fit all 4 L-moment ratios including τ₄ = 0.1366")
    print("• The discrepancy may indicate book used different distribution or method")

if __name__ == "__main__":
    comprehensive_comparison()
