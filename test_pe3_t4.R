
library(lmomco)

# Book's complete values including T4
lambda1_R <- 1.0
tau2_R <- 0.1103
tau3_R <- 0.0279
tau4_R <- 0.1366

cat("R lmomco with book's complete L-moment ratios\n")
cat("==============================================\n")
cat("λ₁ᴿ =", lambda1_R, "\n")
cat("τ₂ᴿ =", tau2_R, "\n")
cat("τ₃ᴿ =", tau3_R, "\n")
cat("τ₄ᴿ =", tau4_R, "\n")
cat("Expected γ = 0.1626\n\n")

# Test 1: With T4 = 0
cat("Test 1: PE3 with T4 = 0\n")
cat("========================\n")
lmoms_t4_0 <- vec2lmom(c(lambda1_R, tau2_R, tau3_R, 0.0))
pe3_t4_0 <- lmom2par(lmoms_t4_0, type="pe3")

if (!is.null(pe3_t4_0) && !is.null(pe3_t4_0$para)) {
  cat("Parameters:\n")
  cat("  μ:", sprintf("%.6f", pe3_t4_0$para[1]), "\n")
  cat("  σ:", sprintf("%.6f", pe3_t4_0$para[2]), "\n")
  cat("  γ:", sprintf("%.6f", pe3_t4_0$para[3]), "\n")
  cat("Difference from book γ:", sprintf("%.6f", abs(pe3_t4_0$para[3] - 0.1626)), "\n\n")
}

# Test 2: With T4 = 0.1366
cat("Test 2: PE3 with T4 = 0.1366\n")
cat("=============================\n")
lmoms_full <- vec2lmom(c(lambda1_R, tau2_R, tau3_R, tau4_R))
pe3_full <- lmom2par(lmoms_full, type="pe3")

if (!is.null(pe3_full) && !is.null(pe3_full$para)) {
  cat("Parameters:\n")
  cat("  μ:", sprintf("%.6f", pe3_full$para[1]), "\n")
  cat("  σ:", sprintf("%.6f", pe3_full$para[2]), "\n")
  cat("  γ:", sprintf("%.6f", pe3_full$para[3]), "\n")
  
  gamma_diff <- abs(pe3_full$para[3] - 0.1626)
  cat("Difference from book γ:", sprintf("%.6f", gamma_diff), "\n")
  
  if (gamma_diff < 0.001) {
    cat("EXCELLENT MATCH!\n")
  } else if (gamma_diff < 0.01) {
    cat("GOOD MATCH!\n")
  }
  
  # Verify fit
  cat("\nVerification (theoretical L-moments):\n")
  theo_lmoms <- par2lmom(pe3_full)
  if (!is.null(theo_lmoms)) {
    cat("  λ₁:", sprintf("%.6f", theo_lmoms$lambdas[1]), "(target:", sprintf("%.4f", lambda1_R), ")\n")
    cat("  τ₂:", sprintf("%.6f", theo_lmoms$ratios[2]), "(target:", sprintf("%.4f", tau2_R), ")\n")
    cat("  τ₃:", sprintf("%.6f", theo_lmoms$ratios[3]), "(target:", sprintf("%.4f", tau3_R), ")\n")
    cat("  τ₄:", sprintf("%.6f", theo_lmoms$ratios[4]), "(target:", sprintf("%.4f", tau4_R), ")\n")
  }
} else {
  cat("Error fitting PE3 with T4\n")
}
