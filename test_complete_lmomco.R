
library(lmomco)

# Regional L-moment ratios
regional_tr <- 0.110298
regional_tr3 <- 0.027859
regional_tr4 <- 0.136613

cat("R lmomco Analysis\n")
cat("=================\n")
cat("Regional L-moment ratios:\n")
cat("  λ₁ᴿ = 1.0000\n")
cat("  τ₂ᴿ =", sprintf("%.6f", regional_tr), "\n")
cat("  τ₃ᴿ =", sprintf("%.6f", regional_tr3), "\n")
cat("  τ₄ᴿ =", sprintf("%.6f", regional_tr4), "\n\n")

# 3-parameter distributions
cat("3-parameter distributions (T4=0):\n")
cat("==================================\n")

lmoms_3param <- vec2lmom(c(1.0, regional_tr, regional_tr3, 0.0))
distributions_3 <- c("kap", "gev", "glo", "gpa", "pe3", "gno", "gam", "wak", "gum")

for (dist in distributions_3) {
  cat("\n", toupper(dist), ":\n", sep="")
  tryCatch({
    params <- lmom2par(lmoms_3param, type=dist)
    if (!is.null(params) && !is.null(params$para)) {
      param_names <- names(params$para)
      if (is.null(param_names)) {
        for (i in 1:length(params$para)) {
          cat("  param", i, ":", sprintf("%.6f", params$para[i]), "\n")
        }
      } else {
        for (i in 1:length(params$para)) {
          cat("  ", param_names[i], ":", sprintf("%.6f", params$para[i]), "\n")
        }
      }
    } else {
      cat("  Failed to fit\n")
    }
  }, error = function(e) {
    cat("  Error:", e$message, "\n")
  })
}

# 4-parameter distributions
cat("\n\n4-parameter distributions (with T4):\n")
cat("====================================\n")

lmoms_4param <- vec2lmom(c(1.0, regional_tr, regional_tr3, regional_tr4))

cat("\nKAPPA (4-param):\n")
tryCatch({
  kap_params <- lmom2par(lmoms_4param, type="kap")
  if (!is.null(kap_params) && !is.null(kap_params$para)) {
    cat("  xi:", sprintf("%.6f", kap_params$para[1]), "\n")
    cat("  alpha:", sprintf("%.6f", kap_params$para[2]), "\n")
    cat("  kappa:", sprintf("%.6f", kap_params$para[3]), "\n")
    cat("  h:", sprintf("%.6f", kap_params$para[4]), "\n")
  }
}, error = function(e) {
  cat("  Error:", e$message, "\n")
})
