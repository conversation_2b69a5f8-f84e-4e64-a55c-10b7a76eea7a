#!/usr/bin/env python3
"""
Complete L-moments comparison using all your data including T4 values.
Compares Python lmoments3, R lmomco, and R lmom packages.
"""

import numpy as np
from lmoments3 import distr
import subprocess
import os

def complete_lmoments_comparison():
    """Compare all three L-moment packages using complete data"""
    
    print("COMPLETE L-MOMENTS COMPARISON")
    print("=" * 60)
    print("Python lmoments3 vs R lmomco vs R lmom")
    print("Using complete data including T4 values")
    print("=" * 60)
    
    # Your complete data
    tr_values = [.1209, .0915, .1124, .1032, .0967, .1328, .1008, .1143, .1107, .1179, 
                 .1308, .1119, .1018, .1025, .1054, .1174, .1115, .1003, .1046]
    
    tr3_values = [.0488, .0105, .0614, .0417, -.0134, -.0176, .0943, .0555, .0478, .0492, 
                  .0940, -.0429, .0435, .0182, -.0224, .0124, -.0346, .0446, .0128]
    
    tr4_values = [.1433, .1569, .1541, .1429, .1568, .1206, .1967, .1210, .1371, .0900, 
                  .1273, .0927, .1446, .1047, .1664, .1317, .1032, .1450, .1583]
    
    rec_length = [98, 59, 90, 61, 65, 86, 78, 72, 67, 99, 49, 61, 69, 73, 70, 66, 59, 74, 82]
    
    # Calculate weighted regional L-moment ratios
    tr_array = np.array(tr_values)
    tr3_array = np.array(tr3_values)
    tr4_array = np.array(tr4_values)
    n_array = np.array(rec_length)
    
    regional_tr = (tr_array * n_array).sum() / n_array.sum()
    regional_tr3 = (tr3_array * n_array).sum() / n_array.sum()
    regional_tr4 = (tr4_array * n_array).sum() / n_array.sum()
    
    print(f"Regional L-moment ratios (weighted by record length):")
    print(f"  λ₁ᴿ = 1.0000")
    print(f"  τ₂ᴿ = {regional_tr:.6f}")
    print(f"  τ₃ᴿ = {regional_tr3:.6f}")
    print(f"  τ₄ᴿ = {regional_tr4:.6f}")
    
    print(f"\nData summary:")
    print(f"  Number of stations: {len(tr_values)}")
    print(f"  Total record length: {n_array.sum()} years")
    print(f"  Record length range: {n_array.min()} to {n_array.max()} years")
    
    # Python lmoments3 analysis
    print(f"\n{'='*60}")
    print("PYTHON lmoments3 RESULTS")
    print(f"{'='*60}")
    
    l1 = 1.0
    lmom_ratios_3param = [l1, regional_tr, regional_tr3, 0.0]
    lmom_ratios_4param = [l1, regional_tr, regional_tr3, regional_tr4]
    
    python_results = {}
    distributions = {
        'KAPPA': distr.kap,
        'GEV': distr.gev,
        'GLO': distr.glo,
        'GPA': distr.gpa,
        'PE3': distr.pe3,
        'GNO': distr.gno,
        'GAM': distr.gam,
        'WAK': distr.wak,
        'GUM': distr.gum
    }
    
    print(f"\nUsing 3-parameter fitting (T4=0):")
    for name, dist in distributions.items():
        try:
            params = dist.lmom_fit(lmom_ratios=lmom_ratios_3param)
            python_results[name + '_3param'] = params
            print(f"\n{name}:")
            for key, value in params.items():
                print(f"  {key}: {value:.6f}")
        except Exception as e:
            print(f"\n{name}: Error - {e}")
    
    print(f"\nUsing 4-parameter fitting (with T4={regional_tr4:.6f}):")
    four_param_dists = ['KAPPA']  # Only KAPPA supports 4 parameters
    
    for name in four_param_dists:
        try:
            dist = distributions[name]
            params = dist.lmom_fit(lmom_ratios=lmom_ratios_4param)
            python_results[name + '_4param'] = params
            print(f"\n{name} (4-param):")
            for key, value in params.items():
                print(f"  {key}: {value:.6f}")
        except Exception as e:
            print(f"\n{name} (4-param): Error - {e}")
    
    # R lmomco analysis
    print(f"\n{'='*60}")
    print("R lmomco ANALYSIS")
    print(f"{'='*60}")
    
    r_lmomco_script = f'''
library(lmomco)

# Regional L-moment ratios
regional_tr <- {regional_tr:.6f}
regional_tr3 <- {regional_tr3:.6f}
regional_tr4 <- {regional_tr4:.6f}

cat("R lmomco Analysis\\n")
cat("=================\\n")
cat("Regional L-moment ratios:\\n")
cat("  λ₁ᴿ = 1.0000\\n")
cat("  τ₂ᴿ =", sprintf("%.6f", regional_tr), "\\n")
cat("  τ₃ᴿ =", sprintf("%.6f", regional_tr3), "\\n")
cat("  τ₄ᴿ =", sprintf("%.6f", regional_tr4), "\\n\\n")

# 3-parameter distributions
cat("3-parameter distributions (T4=0):\\n")
cat("==================================\\n")

lmoms_3param <- vec2lmom(c(1.0, regional_tr, regional_tr3, 0.0))
distributions_3 <- c("kap", "gev", "glo", "gpa", "pe3", "gno", "gam", "wak", "gum")

for (dist in distributions_3) {{
  cat("\\n", toupper(dist), ":\\n", sep="")
  tryCatch({{
    params <- lmom2par(lmoms_3param, type=dist)
    if (!is.null(params) && !is.null(params$para)) {{
      param_names <- names(params$para)
      if (is.null(param_names)) {{
        for (i in 1:length(params$para)) {{
          cat("  param", i, ":", sprintf("%.6f", params$para[i]), "\\n")
        }}
      }} else {{
        for (i in 1:length(params$para)) {{
          cat("  ", param_names[i], ":", sprintf("%.6f", params$para[i]), "\\n")
        }}
      }}
    }} else {{
      cat("  Failed to fit\\n")
    }}
  }}, error = function(e) {{
    cat("  Error:", e$message, "\\n")
  }})
}}

# 4-parameter distributions
cat("\\n\\n4-parameter distributions (with T4):\\n")
cat("====================================\\n")

lmoms_4param <- vec2lmom(c(1.0, regional_tr, regional_tr3, regional_tr4))

cat("\\nKAPPA (4-param):\\n")
tryCatch({{
  kap_params <- lmom2par(lmoms_4param, type="kap")
  if (!is.null(kap_params) && !is.null(kap_params$para)) {{
    cat("  xi:", sprintf("%.6f", kap_params$para[1]), "\\n")
    cat("  alpha:", sprintf("%.6f", kap_params$para[2]), "\\n")
    cat("  kappa:", sprintf("%.6f", kap_params$para[3]), "\\n")
    cat("  h:", sprintf("%.6f", kap_params$para[4]), "\\n")
  }}
}}, error = function(e) {{
  cat("  Error:", e$message, "\\n")
}})
'''
    
    # R lmom analysis
    print(f"\n{'='*60}")
    print("R lmom ANALYSIS")
    print(f"{'='*60}")
    
    r_lmom_script = f'''
library(lmom)

# Regional L-moment ratios
regional_tr <- {regional_tr:.6f}
regional_tr3 <- {regional_tr3:.6f}
regional_tr4 <- {regional_tr4:.6f}

cat("R lmom Analysis\\n")
cat("===============\\n")
cat("Regional L-moment ratios:\\n")
cat("  λ₁ᴿ = 1.0000\\n")
cat("  τ₂ᴿ =", sprintf("%.6f", regional_tr), "\\n")
cat("  τ₃ᴿ =", sprintf("%.6f", regional_tr3), "\\n")
cat("  τ₄ᴿ =", sprintf("%.6f", regional_tr4), "\\n\\n")

# Create L-moments vectors
l1 <- 1.0
l2 <- regional_tr * l1
l3 <- regional_tr3 * l2
l4 <- regional_tr4 * l2  # Note: L4 = T4 * L2

lmoms_3param <- c(l1, l2, l3, 0.0)
lmoms_4param <- c(l1, l2, l3, l4)

cat("L-moments for 3-param fitting:", sprintf("%.6f", lmoms_3param), "\\n")
cat("L-moments for 4-param fitting:", sprintf("%.6f", lmoms_4param), "\\n\\n")

# 3-parameter distributions
cat("3-parameter distributions:\\n")
cat("=========================\\n")

# GEV
tryCatch({{
  gev_params <- pelgev(lmoms_3param)
  cat("\\nGEV:\\n")
  cat("  xi:", sprintf("%.6f", gev_params[1]), "\\n")
  cat("  alpha:", sprintf("%.6f", gev_params[2]), "\\n")
  cat("  kappa:", sprintf("%.6f", gev_params[3]), "\\n")
}}, error = function(e) {{
  cat("\\nGEV: Error -", e$message, "\\n")
}})

# GLO
tryCatch({{
  glo_params <- pelglo(lmoms_3param)
  cat("\\nGLO:\\n")
  cat("  xi:", sprintf("%.6f", glo_params[1]), "\\n")
  cat("  alpha:", sprintf("%.6f", glo_params[2]), "\\n")
  cat("  kappa:", sprintf("%.6f", glo_params[3]), "\\n")
}}, error = function(e) {{
  cat("\\nGLO: Error -", e$message, "\\n")
}})

# GPA
tryCatch({{
  gpa_params <- pelgpa(lmoms_3param)
  cat("\\nGPA:\\n")
  cat("  xi:", sprintf("%.6f", gpa_params[1]), "\\n")
  cat("  alpha:", sprintf("%.6f", gpa_params[2]), "\\n")
  cat("  kappa:", sprintf("%.6f", gpa_params[3]), "\\n")
}}, error = function(e) {{
  cat("\\nGPA: Error -", e$message, "\\n")
}})

# PE3
tryCatch({{
  pe3_params <- pelpe3(lmoms_3param)
  cat("\\nPE3:\\n")
  cat("  mu:", sprintf("%.6f", pe3_params[1]), "\\n")
  cat("  sigma:", sprintf("%.6f", pe3_params[2]), "\\n")
  cat("  gamma:", sprintf("%.6f", pe3_params[3]), "\\n")
}}, error = function(e) {{
  cat("\\nPE3: Error -", e$message, "\\n")
}})

# GNO
tryCatch({{
  gno_params <- pelgno(lmoms_3param)
  cat("\\nGNO:\\n")
  cat("  xi:", sprintf("%.6f", gno_params[1]), "\\n")
  cat("  alpha:", sprintf("%.6f", gno_params[2]), "\\n")
  cat("  kappa:", sprintf("%.6f", gno_params[3]), "\\n")
}}, error = function(e) {{
  cat("\\nGNO: Error -", e$message, "\\n")
}})

# Gumbel
tryCatch({{
  gum_params <- pelgum(lmoms_3param)
  cat("\\nGUM:\\n")
  cat("  xi:", sprintf("%.6f", gum_params[1]), "\\n")
  cat("  alpha:", sprintf("%.6f", gum_params[2]), "\\n")
}}, error = function(e) {{
  cat("\\nGUM: Error -", e$message, "\\n")
}})

# 4-parameter distributions
cat("\\n\\n4-parameter distributions:\\n")
cat("=========================\\n")

# KAPPA
tryCatch({{
  kap_params <- pelkap(lmoms_4param)
  cat("\\nKAPPA (4-param):\\n")
  cat("  xi:", sprintf("%.6f", kap_params[1]), "\\n")
  cat("  alpha:", sprintf("%.6f", kap_params[2]), "\\n")
  cat("  kappa:", sprintf("%.6f", kap_params[3]), "\\n")
  cat("  h:", sprintf("%.6f", kap_params[4]), "\\n")
}}, error = function(e) {{
  cat("\\nKAPPA: Error -", e$message, "\\n")
}})
'''
    
    # Run R scripts
    scripts = [
        ("lmomco", r_lmomco_script, "test_complete_lmomco.R"),
        ("lmom", r_lmom_script, "test_complete_lmom.R")
    ]
    
    for package_name, script_content, filename in scripts:
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            # Try to run R script
            rscript_paths = [
                'Rscript',
                r'C:\Program Files\R\R-4.3.0\bin\x64\Rscript.exe',
                r'C:\Program Files\R\R-4.3.0\bin\Rscript.exe'
            ]
            
            result = None
            for rscript_path in rscript_paths:
                try:
                    result = subprocess.run(
                        [rscript_path, filename], 
                        capture_output=True, 
                        text=True,
                        cwd=os.getcwd()
                    )
                    break
                except FileNotFoundError:
                    continue
            
            if result and result.returncode == 0:
                print(result.stdout)
                if result.stderr:
                    print("Warnings:")
                    print(result.stderr)
            else:
                print(f"Could not run R {package_name} script")
                
        except Exception as e:
            print(f"Error with R {package_name} script: {e}")
    
    # Summary comparison
    print(f"\n{'='*60}")
    print("SUMMARY COMPARISON")
    print(f"{'='*60}")
    
    print("Key findings:")
    print("• Complete data includes T4 values for all 19 stations")
    print(f"• Regional τ₄ᴿ = {regional_tr4:.6f} (calculated from your data)")
    print("• All three packages tested with same L-moment ratios")
    print("• KAPPA distribution can utilize all 4 L-moment ratios")
    print("• 3-parameter distributions ignore T4 and should give consistent results")
    print("• Comparison shows which package implementations are most reliable")

if __name__ == "__main__":
    complete_lmoments_comparison()
