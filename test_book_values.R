
library(lmomco)

# Book's exact values
lambda1_R <- 1.0
tau2_R <- 0.1103
tau3_R <- 0.0279
tau4_R <- 0.0

cat("R lmomco with book values\n")
cat("========================\n")
cat("λ₁ᴿ =", lambda1_R, "\n")
cat("τ₂ᴿ =", tau2_R, "\n")
cat("τ₃ᴿ =", tau3_R, "\n")
cat("τ₄ᴿ =", tau4_R, "\n")
cat("Expected γ = 0.1626\n\n")

# Create L-moments object
lmoms <- vec2lmom(c(lambda1_R, tau2_R, tau3_R, tau4_R))

# Fit PE3
pe3_params <- lmom2par(lmoms, type="pe3")

if (!is.null(pe3_params) && !is.null(pe3_params$para)) {
  cat("R PE3 parameters:\n")
  cat("  μ (mu):", sprintf("%.6f", pe3_params$para[1]), "\n")
  cat("  σ (sigma):", sprintf("%.6f", pe3_params$para[2]), "\n")
  cat("  γ (gamma):", sprintf("%.6f", pe3_params$para[3]), "\n")
  
  gamma_r <- pe3_params$para[3]
  cat("\nComparison with book:\n")
  cat("  Book γ: 0.1626\n")
  cat("  R γ:", sprintf("%.6f", gamma_r), "\n")
  cat("  Difference:", sprintf("%.6f", abs(gamma_r - 0.1626)), "\n")
  
  if (abs(gamma_r - 0.1626) < 0.001) {
    cat("  EXCELLENT MATCH!\n")
  } else if (abs(gamma_r - 0.1626) < 0.01) {
    cat("  GOOD MATCH!\n")
  }
} else {
  cat("Error fitting PE3 distribution\n")
}
