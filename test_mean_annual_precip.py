#!/usr/bin/env python3
"""
Test script to verify the mean annual precipitation calculation.

This script creates test data and verifies that the mean annual precipitation
is calculated correctly as the mean over all years of the yearly mean 
precipitation at each grid point for each weather type.
"""

import os
import sys
import numpy as np
import xarray as xr
import tempfile
import logging

# Add src to path
sys.path.insert(0, 'src')

from models.precipitation import PrecipitationAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data_with_known_annual_means():
    """Create test data with known annual mean patterns for verification."""
    
    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    logger.info(f"Creating test data in: {temp_dir}")
    
    # Grid parameters
    ny, nx = 5, 6
    n_years = 3
    days_per_year = 365
    n_days = n_years * days_per_year
    
    # Create coordinates
    lat_coords = np.linspace(35, 40, ny)
    lon_coords = np.linspace(-120, -115, nx)
    time_coords = np.arange(n_days)
    
    # Create precipitation data with known patterns
    # We'll create data where:
    # - WT 0 has higher precipitation in first half of each year
    # - WT 1 has higher precipitation in second half of each year
    # This will create different annual means for each WT
    
    np.random.seed(42)
    precip_data = np.zeros((n_days, ny, nx))
    cluster_assignments = np.zeros(n_days, dtype=int)
    
    for year in range(n_years):
        year_start = year * days_per_year
        year_end = (year + 1) * days_per_year
        
        # First half of year: mostly WT 0 with higher precip
        first_half_end = year_start + days_per_year // 2
        cluster_assignments[year_start:first_half_end] = 0
        precip_data[year_start:first_half_end] = np.random.gamma(3, 5, (first_half_end - year_start, ny, nx))
        
        # Second half of year: mostly WT 1 with lower precip
        cluster_assignments[first_half_end:year_end] = 1
        precip_data[first_half_end:year_end] = np.random.gamma(2, 2, (year_end - first_half_end, ny, nx))
    
    # Calculate expected annual means manually for verification
    expected_annual_means = {}
    for wt in [0, 1]:
        wt_annual_means = []
        for year in range(n_years):
            year_start = year * days_per_year
            year_end = (year + 1) * days_per_year
            
            year_mask = cluster_assignments[year_start:year_end] == wt
            if np.any(year_mask):
                year_precip = precip_data[year_start:year_end][year_mask]
                annual_mean = np.mean(year_precip, axis=0)
                wt_annual_means.append(annual_mean)
        
        if wt_annual_means:
            expected_annual_means[wt] = np.mean(wt_annual_means, axis=0)
        else:
            expected_annual_means[wt] = np.full((ny, nx), np.nan)
    
    logger.info(f"Expected annual means - WT 0: {np.nanmean(expected_annual_means[0]):.2f}, WT 1: {np.nanmean(expected_annual_means[1]):.2f}")
    
    # Save precipitation data
    precip_ds = xr.Dataset(
        {"precip": (["time", "y", "x"], precip_data)},
        coords={"time": time_coords, "y": lat_coords, "x": lon_coords}
    )
    precip_path = os.path.join(temp_dir, "precip.nc")
    precip_ds.to_netcdf(precip_path)
    
    # Save cluster data
    cluster_data = {"k=2": (["time"], cluster_assignments)}
    cluster_ds = xr.Dataset(cluster_data, coords={"time": time_coords})
    cluster_path = os.path.join(temp_dir, "clusters.nc")
    cluster_ds.to_netcdf(cluster_path)
    
    return temp_dir, precip_path, cluster_path, expected_annual_means

def test_mean_annual_precip():
    """Test the mean annual precipitation calculation."""
    
    logger.info("Creating test data with known annual patterns...")
    temp_dir, precip_path, cluster_path, expected_annual_means = create_test_data_with_known_annual_means()
    
    try:
        # Initialize analyzer
        output_path = os.path.join(temp_dir, "output_annual_precip.nc")
        
        logger.info("Initializing PrecipitationAnalyzer...")
        analyzer = PrecipitationAnalyzer(
            precip_path=precip_path,
            cluster_path=cluster_path,
            output_path=output_path,
            max_k=2
        )
        
        # Run the analysis
        logger.info("Running analysis...")
        analyzer.run()
        
        # Check the output
        logger.info("Checking output file...")
        output_ds = xr.open_dataset(output_path)
        
        logger.info(f"Output variables: {list(output_ds.data_vars.keys())}")
        
        # Check for mean annual precipitation variable
        if 'mean_annual_precip' in output_ds.data_vars:
            logger.info("✅ Found mean_annual_precip variable!")
            
            # Get the calculated values
            calculated_annual_precip = output_ds['mean_annual_precip']
            logger.info(f"mean_annual_precip shape: {calculated_annual_precip.shape}")
            logger.info(f"mean_annual_precip dimensions: {calculated_annual_precip.dims}")
            
            # Check k=2 results
            k2_results = calculated_annual_precip.sel(k=2)
            
            for wt in [0, 1]:
                calculated_mean = k2_results.sel(cluster=wt).values
                expected_mean = expected_annual_means[wt]
                
                logger.info(f"WT {wt}:")
                logger.info(f"  Calculated mean: {np.nanmean(calculated_mean):.2f}")
                logger.info(f"  Expected mean: {np.nanmean(expected_mean):.2f}")
                
                # Check if values are reasonably close (within 10% tolerance)
                if not np.allclose(calculated_mean, expected_mean, rtol=0.1, equal_nan=True):
                    logger.warning(f"  ⚠️  Values don't match closely for WT {wt}")
                    logger.warning(f"  Max difference: {np.nanmax(np.abs(calculated_mean - expected_mean)):.2f}")
                else:
                    logger.info(f"  ✅ Values match expected for WT {wt}")
            
            # Check attributes
            attrs = calculated_annual_precip.attrs
            logger.info(f"Variable attributes: {attrs}")
            
            if 'long_name' in attrs and 'mean annual precipitation' in attrs['long_name'].lower():
                logger.info("✅ Correct long_name attribute")
            else:
                logger.warning("⚠️  Missing or incorrect long_name attribute")
                
            if 'units' in attrs:
                logger.info(f"✅ Units: {attrs['units']}")
            else:
                logger.warning("⚠️  Missing units attribute")
                
        else:
            logger.error("❌ mean_annual_precip variable not found in output!")
            
        output_ds.close()
        
        logger.info("✅ Mean annual precipitation test completed!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        # Clean up
        import shutil
        shutil.rmtree(temp_dir)
        logger.info("Cleaned up test data")

if __name__ == "__main__":
    test_mean_annual_precip()
