#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
<PERSON><PERSON><PERSON> to run heterogeneity analysis on precipitation clusters.

This script calculates heterogeneity statistics (H1, H2, H3) using L-moments
as described in <PERSON><PERSON><PERSON> and <PERSON> (1997) for precipitation clusters.
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# Add the src directory to the path so we can import our modules
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.models.heterogeneity import HeterogeneityAnalysis
from src.utils.logging_config import setup_logging

logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Calculate heterogeneity statistics for precipitation clusters.'
    )
    
    parser.add_argument(
        '--precip-path',
        type=str,
        required=True,
        help='Path to the precipitation data file (NetCDF) containing AMS series'
    )
    
    parser.add_argument(
        '--cluster-path',
        type=str,
        required=True,
        help='Path to the cluster data file containing cluster assignments for each grid point'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        default='heterogeneity_results',
        help='Directory to save the results (default: heterogeneity_results)'
    )
    
    parser.add_argument(
        '--max-clusters',
        type=int,
        default=20,
        help='Maximum number of clusters to analyze (default: 20)'
    )
    
    parser.add_argument(
        '--nsim',
        type=int,
        default=500,
        help='Number of simulations for the heterogeneity test (default: 500)'
    )
    
    parser.add_argument(
        '--seed',
        type=int,
        default=42,
        help='Random seed for reproducibility (default: 42)'
    )
    
    parser.add_argument(
        '--log-level',
        type=str,
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default='INFO',
        help='Set the logging level (default: INFO)'
    )
    
    return parser.parse_args()

def main():
    """Run the heterogeneity analysis."""
    # Parse command line arguments
    args = parse_args()
    
    # Setup logging
    log_level = getattr(logging, args.log_level)
    setup_logging(log_level=log_level)
    
    logger.info("Starting heterogeneity analysis")
    logger.info(f"Precipitation data: {args.precip_path}")
    logger.info(f"Cluster data: {args.cluster_path}")
    logger.info(f"Output directory: {args.output_dir}")
    logger.info(f"Maximum clusters: {args.max_clusters}")
    logger.info(f"Number of simulations: {args.nsim}")
    
    try:
        # Create output directory if it doesn't exist
        os.makedirs(args.output_dir, exist_ok=True)
        
        # Initialize heterogeneity analysis
        analysis = HeterogeneityAnalysis(
            precip_path=args.precip_path,
            cluster_path=args.cluster_path,
            output_dir=args.output_dir,
            nsim=args.nsim,
            max_clusters=args.max_clusters,
            seed=args.seed
        )
        
        # Load data
        logger.info("Loading data")
        analysis.load_data()
        
        # Run analysis
        logger.info("Running heterogeneity analysis")
        results = analysis.run_analysis()
        
        # Create summary table
        logger.info("Creating summary tables")
        analysis.create_summary_table()
        
        # Create plots
        logger.info("Creating plots")
        analysis.plot_heterogeneity_statistics()
        
        logger.info("Heterogeneity analysis completed successfully")
        
    except Exception as e:
        logger.error(f"Error in heterogeneity analysis: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
