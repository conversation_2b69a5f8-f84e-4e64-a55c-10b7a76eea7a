#!/usr/bin/env python3
"""
Investigate PE3 gamma parameter discrepancy between calculated value and book value.
"""

import numpy as np
from lmoments3 import distr
import math

def investigate_gamma_discrepancy():
    """Investigate why gamma differs from book value"""
    
    print("PE3 GAMMA PARAMETER INVESTIGATION")
    print("=" * 50)
    
    # Your data
    tr_values = [.1209, .0915, .1124, .1032, .0967, .1328, .1008, .1143, .1107, .1179, 
                 .1308, .1119, .1018, .1025, .1054, .1174, .1115, .1003, .1046]
    
    tr3_values = [.0488, .0105, .0614, .0417, -.0134, -.0176, .0943, .0555, .0478, .0492, 
                  .0940, -.0429, .0435, .0182, -.0224, .0124, -.0346, .0446, .0128]
    
    rec_length = [98, 59, 90, 61, 65, 86, 78, 72, 67, 99, 49, 61, 69, 73, 70, 66, 59, 74, 82]
    
    # Calculate weighted regional L-moment ratios
    tr_array = np.array(tr_values)
    tr3_array = np.array(tr3_values)
    rec_array = np.array(rec_length)
    
    regional_tr = (tr_array * rec_array).sum() / rec_array.sum()
    regional_tr3 = (tr3_array * rec_array).sum() / rec_array.sum()
    
    print(f"Regional TR: {regional_tr:.6f}")
    print(f"Regional TR3: {regional_tr3:.6f}")
    print(f"Book gamma: 0.1626")
    print(f"Calculated gamma: 0.170991")
    print(f"Difference: {abs(0.170991 - 0.1626):.6f}")
    
    # Test 1: Check if book used unweighted average
    print(f"\n{'='*50}")
    print("TEST 1: UNWEIGHTED VS WEIGHTED AVERAGES")
    print(f"{'='*50}")
    
    unweighted_tr = np.mean(tr_values)
    unweighted_tr3 = np.mean(tr3_values)
    
    print(f"Unweighted TR: {unweighted_tr:.6f}")
    print(f"Unweighted TR3: {unweighted_tr3:.6f}")
    print(f"Weighted TR: {regional_tr:.6f}")
    print(f"Weighted TR3: {regional_tr3:.6f}")
    
    # Fit PE3 with unweighted values
    l1 = 1.0
    unweighted_lmoms = [l1, unweighted_tr * l1, unweighted_tr3, 0.0]
    
    try:
        unweighted_pe3 = distr.pe3.lmom_fit(lmom_ratios=unweighted_lmoms)
        print(f"\nPE3 with unweighted averages:")
        print(f"  gamma: {unweighted_pe3['skew']:.6f}")
        print(f"  Difference from book: {abs(unweighted_pe3['skew'] - 0.1626):.6f}")
    except Exception as e:
        print(f"Error with unweighted PE3: {e}")
    
    # Test 2: Check different calculation methods
    print(f"\n{'='*50}")
    print("TEST 2: DIFFERENT GAMMA CALCULATION METHODS")
    print(f"{'='*50}")
    
    # Method A: Standard PE3 relationship T3 = (2/π) * arctan(γ/2)
    # Solving: γ = 2 * tan(π * T3 / 2)
    gamma_method_a = 2 * math.tan(math.pi * regional_tr3 / 2)
    print(f"Method A (standard formula): {gamma_method_a:.6f}")
    
    # Method B: Alternative PE3 relationship (some books use different conventions)
    # T3 ≈ γ/π for small γ, so γ ≈ T3 * π
    gamma_method_b = regional_tr3 * math.pi
    print(f"Method B (small gamma approx): {gamma_method_b:.6f}")
    
    # Method C: Check if book used a different T3 value
    book_gamma = 0.1626
    implied_t3_a = (2/math.pi) * math.atan(book_gamma/2)
    implied_t3_b = book_gamma / math.pi
    
    print(f"\nReverse calculation from book gamma = 0.1626:")
    print(f"  Implied T3 (method A): {implied_t3_a:.6f}")
    print(f"  Implied T3 (method B): {implied_t3_b:.6f}")
    print(f"  Actual T3: {regional_tr3:.6f}")
    
    # Test 3: Check if there's a systematic bias in the data
    print(f"\n{'='*50}")
    print("TEST 3: DATA ANALYSIS")
    print(f"{'='*50}")
    
    print(f"Station data summary:")
    print(f"  Number of stations: {len(tr_values)}")
    print(f"  TR range: {min(tr_values):.4f} to {max(tr_values):.4f}")
    print(f"  TR3 range: {min(tr3_values):.4f} to {max(tr3_values):.4f}")
    print(f"  Record length range: {min(rec_length)} to {max(rec_length)} years")
    
    # Check if removing extreme values changes the result
    tr_sorted = sorted(tr_values)
    tr3_sorted = sorted(tr3_values)
    
    # Remove top and bottom 10% (trimmed mean)
    n = len(tr_values)
    trim_count = max(1, n // 10)
    
    tr_trimmed = tr_sorted[trim_count:-trim_count]
    tr3_trimmed = tr3_sorted[trim_count:-trim_count]
    
    trimmed_tr = np.mean(tr_trimmed)
    trimmed_tr3 = np.mean(tr3_trimmed)
    
    print(f"\nTrimmed means (removing {trim_count} extreme values each end):")
    print(f"  Trimmed TR: {trimmed_tr:.6f}")
    print(f"  Trimmed TR3: {trimmed_tr3:.6f}")
    
    try:
        trimmed_lmoms = [l1, trimmed_tr * l1, trimmed_tr3, 0.0]
        trimmed_pe3 = distr.pe3.lmom_fit(lmom_ratios=trimmed_lmoms)
        print(f"  Trimmed gamma: {trimmed_pe3['skew']:.6f}")
        print(f"  Difference from book: {abs(trimmed_pe3['skew'] - 0.1626):.6f}")
    except Exception as e:
        print(f"Error with trimmed PE3: {e}")
    
    # Test 4: Check if book used different L-moment definitions
    print(f"\n{'='*50}")
    print("TEST 4: ALTERNATIVE L-MOMENT DEFINITIONS")
    print(f"{'='*50}")
    
    # Some older references use slightly different L-moment definitions
    # Check if using L-CV and L-skewness directly gives different results
    
    # Alternative: Use L2 and L3 directly instead of ratios
    l2 = regional_tr * l1
    l3 = regional_tr3 * l2  # L3 = T3 * L2
    
    print(f"Direct L-moments:")
    print(f"  L1: {l1:.6f}")
    print(f"  L2: {l2:.6f}")
    print(f"  L3: {l3:.6f}")
    print(f"  L3/L2 (should equal T3): {l3/l2:.6f}")
    
    # Test 5: Check if the book value is actually correct
    print(f"\n{'='*50}")
    print("TEST 5: VERIFICATION OF BOOK VALUE")
    print(f"{'='*50}")
    
    # Use the book gamma value and see what T3 it produces
    book_gamma = 0.1626
    book_mu = 1.0  # Assuming same as our calculation
    book_sigma = 0.195678  # Assuming same as our calculation
    
    # Create PE3 distribution with book parameters
    book_params = {'skew': book_gamma, 'loc': book_mu, 'scale': book_sigma}
    
    try:
        book_lmoms = distr.pe3.lmom_ratios(**book_params)
        print(f"L-moments from book gamma = 0.1626:")
        print(f"  L1: {book_lmoms[0]:.6f}")
        print(f"  TR: {book_lmoms[1]:.6f}")
        print(f"  T3: {book_lmoms[2]:.6f}")
        print(f"  T4: {book_lmoms[3]:.6f}")
        
        print(f"\nComparison with target values:")
        print(f"  TR difference: {abs(book_lmoms[1] - regional_tr):.6f}")
        print(f"  T3 difference: {abs(book_lmoms[2] - regional_tr3):.6f}")
        
        if abs(book_lmoms[2] - regional_tr3) < 0.001:
            print("  Book gamma produces correct T3!")
        else:
            print("  Book gamma does NOT produce correct T3")
            
    except Exception as e:
        print(f"Error testing book parameters: {e}")
    
    # Final conclusion
    print(f"\n{'='*50}")
    print("CONCLUSION")
    print(f"{'='*50}")
    
    print("Both Python lmoments3 and R lmomco give gamma ≈ 0.171")
    print("This is consistent and mathematically correct for the given T3 = 0.027859")
    print("The book value of 0.1626 may be:")
    print("  1. Based on different input data")
    print("  2. Using unweighted averages")
    print("  3. Rounded or approximated")
    print("  4. Using a different parameter convention")
    print("  5. An error in the book")
    
    print(f"\nRecommendation: Use the calculated value of 0.171")
    print(f"This is mathematically consistent with your L-moment ratios.")

if __name__ == "__main__":
    investigate_gamma_discrepancy()
