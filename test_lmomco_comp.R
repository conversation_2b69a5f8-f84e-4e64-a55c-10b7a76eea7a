
library(lmomco)

# Data
regional_tr <- 0.110298
regional_tr3 <- 0.027859
book_tau2 <- 0.1103
book_tau3 <- 0.0279
book_tau4 <- 0.1366
book_gamma <- 0.1626

cat("R lmomco Analysis\n")
cat("=================\n")

# Test with our calculated values
cat("\nUsing our calculated values:\n")
cat("τ₂ =", sprintf("%.6f", regional_tr), "\n")
cat("τ₃ =", sprintf("%.6f", regional_tr3), "\n")

lmoms_our <- vec2lmom(c(1.0, regional_tr, regional_tr3, 0.0))
distributions <- c("kap", "gev", "glo", "gpa", "pe3", "gno")

for (dist in distributions) {
  cat("\n", toupper(dist), ":\n", sep="")
  tryCatch({
    params <- lmom2par(lmoms_our, type=dist)
    if (!is.null(params) && !is.null(params$para)) {
      for (i in 1:length(params$para)) {
        cat("  param", i, ":", sprintf("%.6f", params$para[i]), "\n")
      }
      if (dist == "pe3") {
        gamma_diff <- abs(params$para[3] - book_gamma)
        cat("  PE3 γ diff from book:", sprintf("%.6f", gamma_diff), "\n")
      }
    }
  }, error = function(e) {
    cat("  Error:", e$message, "\n")
  })
}

# Test with book values
cat("\n\nUsing book values:\n")
cat("τ₂ =", sprintf("%.4f", book_tau2), "\n")
cat("τ₃ =", sprintf("%.4f", book_tau3), "\n")

lmoms_book <- vec2lmom(c(1.0, book_tau2, book_tau3, 0.0))

for (dist in distributions) {
  cat("\n", toupper(dist), ":\n", sep="")
  tryCatch({
    params <- lmom2par(lmoms_book, type=dist)
    if (!is.null(params) && !is.null(params$para)) {
      for (i in 1:length(params$para)) {
        cat("  param", i, ":", sprintf("%.6f", params$para[i]), "\n")
      }
      if (dist == "pe3") {
        gamma_diff <- abs(params$para[3] - book_gamma)
        cat("  PE3 γ diff from book:", sprintf("%.6f", gamma_diff), "\n")
      }
    }
  }, error = function(e) {
    cat("  Error:", e$message, "\n")
  })
}

# Test with book values including T4
cat("\n\nUsing book values with T4:\n")
cat("τ₂ =", sprintf("%.4f", book_tau2), "\n")
cat("τ₃ =", sprintf("%.4f", book_tau3), "\n")
cat("τ₄ =", sprintf("%.4f", book_tau4), "\n")

lmoms_book_t4 <- vec2lmom(c(1.0, book_tau2, book_tau3, book_tau4))

cat("\nKAPPA (4-parameter):\n")
tryCatch({
  kap_params <- lmom2par(lmoms_book_t4, type="kap")
  if (!is.null(kap_params) && !is.null(kap_params$para)) {
    cat("  xi:", sprintf("%.6f", kap_params$para[1]), "\n")
    cat("  alpha:", sprintf("%.6f", kap_params$para[2]), "\n")
    cat("  kappa:", sprintf("%.6f", kap_params$para[3]), "\n")
    cat("  h:", sprintf("%.6f", kap_params$para[4]), "\n")
    
    # Check if kappa parameter matches book gamma
    kappa_diff <- abs(kap_params$para[3] - book_gamma)
    cat("  KAPPA κ diff from book γ:", sprintf("%.6f", kappa_diff), "\n")
    if (kappa_diff < 0.01) {
      cat("  *** KAPPA κ close to book PE3 γ! ***\n")
    }
  }
}, error = function(e) {
  cat("  Error:", e$message, "\n")
})
