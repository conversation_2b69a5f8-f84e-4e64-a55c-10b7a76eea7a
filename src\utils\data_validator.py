# -*- coding: utf-8 -*-
"""
Data validation utilities for the weather typing and precipitation regionalization project.
"""
import os
import numpy as np
import xarray as xr
import logging

logger = logging.getLogger(__name__)

class DataValidator:
    """Validator for input data files."""
    
    @staticmethod
    def validate_netcdf_file(file_path, required_vars=None, required_dims=None):
        """
        Validate a NetCDF file.
        
        Parameters:
        -----------
        file_path : str
            Path to the NetCDF file
        required_vars : list, optional
            List of required variables
        required_dims : list, optional
            List of required dimensions
            
        Returns:
        --------
        bool
            True if valid, False otherwise
        """
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return False
        
        try:
            ds = xr.open_dataset(file_path)
            
            # Check required variables
            if required_vars:
                missing_vars = [var for var in required_vars if var not in ds.variables]
                if missing_vars:
                    logger.error(f"Missing required variables in {file_path}: {missing_vars}")
                    return False
            
            # Check required dimensions
            if required_dims:
                missing_dims = [dim for dim in required_dims if dim not in ds.dims]
                if missing_dims:
                    logger.error(f"Missing required dimensions in {file_path}: {missing_dims}")
                    return False
            
            # Check for NaN values
            for var_name in ds.variables:
                if var_name in ds.coords:
                    continue
                
                var = ds[var_name]
                if np.issubdtype(var.dtype, np.number):
                    nan_count = np.isnan(var.values).sum()
                    if nan_count > 0:
                        logger.warning(f"Variable {var_name} in {file_path} contains {nan_count} NaN values")
            
            ds.close()
            return True
            
        except Exception as e:
            logger.error(f"Error validating {file_path}: {str(e)}")
            return False
    
    @staticmethod
    def validate_atmospheric_data(file_path, var_name, level=None):
        """
        Validate atmospheric data file.
        
        Parameters:
        -----------
        file_path : str
            Path to the NetCDF file
        var_name : str
            Variable name
        level : int, optional
            Pressure level
            
        Returns:
        --------
        bool
            True if valid, False otherwise
        """
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return False
        
        try:
            ds = xr.open_dataset(file_path)
            
            # Check if variable exists
            if var_name not in ds.variables:
                logger.error(f"Variable {var_name} not found in {file_path}")
                return False
            
            # Check if level exists (if specified)
            if level is not None:
                if 'pressure_level' not in ds.dims:
                    logger.error(f"Dimension 'pressure_level' not found in {file_path}")
                    return False
                
                if level not in ds.pressure_level.values:
                    logger.error(f"Pressure level {level} not found in {file_path}")
                    return False
            
            # Check time dimension
            if 'time' not in ds.dims:
                logger.error(f"Dimension 'time' not found in {file_path}")
                return False
            
            # Check for NaN values
            var = ds[var_name]
            if np.issubdtype(var.dtype, np.number):
                nan_count = np.isnan(var.values).sum()
                if nan_count > 0:
                    logger.warning(f"Variable {var_name} in {file_path} contains {nan_count} NaN values")
            
            ds.close()
            return True
            
        except Exception as e:
            logger.error(f"Error validating {file_path}: {str(e)}")
            return False
    
    @staticmethod
    def validate_precipitation_data(file_path, var_name='precip'):
        """
        Validate precipitation data file.
        
        Parameters:
        -----------
        file_path : str
            Path to the NetCDF file
        var_name : str, optional
            Variable name (default: 'precip')
            
        Returns:
        --------
        bool
            True if valid, False otherwise
        """
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return False
        
        try:
            ds = xr.open_dataset(file_path)
            
            # Check if variable exists
            if var_name not in ds.variables:
                logger.error(f"Variable {var_name} not found in {file_path}")
                return False
            
            # Check time dimension
            if 'time' not in ds.dims:
                logger.error(f"Dimension 'time' not found in {file_path}")
                return False
            
            # Check for negative values
            var = ds[var_name]
            if np.issubdtype(var.dtype, np.number):
                neg_count = (var.values < 0).sum()
                if neg_count > 0:
                    logger.warning(f"Variable {var_name} in {file_path} contains {neg_count} negative values")
            
            # Check for NaN values
            if np.issubdtype(var.dtype, np.number):
                nan_count = np.isnan(var.values).sum()
                if nan_count > 0:
                    logger.warning(f"Variable {var_name} in {file_path} contains {nan_count} NaN values")
            
            ds.close()
            return True
            
        except Exception as e:
            logger.error(f"Error validating {file_path}: {str(e)}")
            return False
