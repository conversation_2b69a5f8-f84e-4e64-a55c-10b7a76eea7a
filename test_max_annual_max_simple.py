#!/usr/bin/env python3
"""
Simple test script for the new max_annual_max variable.
Tests with small synthetic data to verify the calculation is working.
"""

import os
import tempfile
import numpy as np
import xarray as xr
import pandas as pd
import logging
from src.models.precipitation import PrecipitationAnalyzer

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_simple_test_data():
    """Create simple synthetic test data."""
    temp_dir = tempfile.mkdtemp()
    logger.info(f"Creating simple test data in {temp_dir}")
    
    # Small dataset: 3 years, 5x5 grid
    n_years = 3
    days_per_year = 100  # Reduced for faster testing
    n_times = n_years * days_per_year
    ny, nx = 5, 5
    
    # Create time coordinates
    start_date = pd.Timestamp('2000-01-01')
    time_coords = pd.date_range(start_date, periods=n_times, freq='D')
    
    # Create spatial coordinates
    lat_coords = np.linspace(30, 40, ny)
    lon_coords = np.linspace(-110, -100, nx)
    
    # Create simple precipitation data
    np.random.seed(42)  # For reproducible results
    precip_data = np.random.exponential(scale=5.0, size=(n_times, ny, nx))
    
    # Create simple cluster assignments (alternating between 0 and 1)
    cluster_assignments = np.tile([0, 1], n_times // 2 + 1)[:n_times]
    
    logger.info(f"Created precipitation data with shape {precip_data.shape}")
    logger.info(f"Precipitation range: {np.min(precip_data):.2f} to {np.max(precip_data):.2f}")
    
    # Save precipitation data
    precip_ds = xr.Dataset(
        {"precip": (["time", "y", "x"], precip_data)},
        coords={"time": time_coords, "y": lat_coords, "x": lon_coords}
    )
    precip_path = os.path.join(temp_dir, "precip.nc")
    precip_ds.to_netcdf(precip_path)
    
    # Save cluster data
    cluster_data = {"k=2": (["time"], cluster_assignments)}
    cluster_ds = xr.Dataset(cluster_data, coords={"time": time_coords})
    cluster_path = os.path.join(temp_dir, "clusters.nc")
    cluster_ds.to_netcdf(cluster_path)
    
    return temp_dir, precip_path, cluster_path

def test_max_annual_max_simple():
    """Test the max annual maximum precipitation calculation with simple data."""
    
    logger.info("Creating simple test data...")
    temp_dir, precip_path, cluster_path = create_simple_test_data()
    
    try:
        # Initialize analyzer
        output_path = os.path.join(temp_dir, "output_simple.nc")
        
        logger.info("Initializing PrecipitationAnalyzer...")
        analyzer = PrecipitationAnalyzer(
            precip_path=precip_path,
            cluster_path=cluster_path,
            output_path=output_path,
            max_k=2
        )
        
        # Run the analysis
        logger.info("Running analysis...")
        analyzer.run()
        
        # Check the output
        logger.info("Checking output file...")
        output_ds = xr.open_dataset(output_path)
        
        logger.info(f"Output variables: {list(output_ds.data_vars.keys())}")
        
        # Verify max_annual_max variable exists
        assert "max_annual_max" in output_ds.data_vars, "max_annual_max variable not found in output"
        logger.info("✓ max_annual_max variable found in output")
        
        # Check that max_annual_max >= mean_annual_max
        max_annual_max_data = output_ds["max_annual_max"].sel(k=2)
        mean_annual_max_data = output_ds["mean_annual_max"].sel(k=2)
        
        for cluster in [0, 1]:
            max_vals = max_annual_max_data.sel(cluster=cluster).values
            mean_vals = mean_annual_max_data.sel(cluster=cluster).values
            
            # Check that max >= mean (allowing for small numerical differences)
            assert np.all(max_vals >= mean_vals - 1e-10), f"max_annual_max should be >= mean_annual_max for cluster {cluster}"
            
            logger.info(f"✓ Cluster {cluster}: max_annual_max ({np.nanmean(max_vals):.2f}) >= mean_annual_max ({np.nanmean(mean_vals):.2f})")
        
        # Check attributes
        max_annual_max_var = output_ds["max_annual_max"]
        assert "long_name" in max_annual_max_var.attrs, "max_annual_max missing long_name attribute"
        assert "description" in max_annual_max_var.attrs, "max_annual_max missing description attribute"
        assert "units" in max_annual_max_var.attrs, "max_annual_max missing units attribute"
        logger.info("✓ max_annual_max has required attributes")
        
        logger.info("✓ Simple test passed!")
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up
        import shutil
        shutil.rmtree(temp_dir)
        logger.info(f"Cleaned up temporary directory: {temp_dir}")

if __name__ == "__main__":
    logger.info("Testing max annual maximum precipitation calculation (simple)...")
    success = test_max_annual_max_simple()
    
    if success:
        logger.info("✅ Simple test passed successfully!")
    else:
        logger.error("❌ Simple test failed!")
        exit(1)
