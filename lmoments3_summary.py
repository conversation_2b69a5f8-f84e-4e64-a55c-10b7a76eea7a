#!/usr/bin/env python3
"""
Summary of lmoments3 testing results and demonstration of random sampling.
"""

import numpy as np
import matplotlib.pyplot as plt
from lmoments3 import distr

def demonstrate_sampling():
    """Demonstrate random sampling from fitted distributions"""
    
    # Your data
    tr_values = [.1209,.0915,.1124,.1032,.0967,.1328,.1008,.1143,.1107,.1179,
                 .1308,.1119,.1018,.1025,.1054,.1174,.1115,.1003,.1046]
    tr3_values = [.0488,.0105,.0614,.0417,-.0134,-.0176,.0943,.0555,.0478,.0492,
                  .0940,-.0429,.0435,.0182,-.0224,.0124,-.0346,.0446,.0128]
    rec_length = [98,59,90,61,65,86,78,72,67,99,49,61,69,73,70,66,59,74,82]

    # Calculate weighted regional L-moment ratios
    tr_array = np.array(tr_values)
    tr3_array = np.array(tr3_values)
    rec_array = np.array(rec_length)

    simple_mean_tr = np.mean(tr_values)
    simple_mean_tr3 = np.mean(tr3_values)

    # Regional L-moment ratios (weighted averages)
    mean_tr = (tr_array * rec_array).sum() / rec_array.sum()
    mean_tr3 = (tr3_array * rec_array).sum() / rec_array.sum()

    print("LMOMENTS3 LIBRARY TEST SUMMARY")
    print("=" * 50)
    print(f"Simple Mean TR (L-CV): {simple_mean_tr:.6f}")
    print(f"Simple Mean TR3 (L-Skewness): {simple_mean_tr3:.6f}")
    print(f"Regional TR (weighted): {mean_tr:.6f}")
    print(f"Regional TR3 (weighted): {mean_tr3:.6f}")
    print(f"Total record length: {rec_array.sum()}")
    
    # Fit distributions using mean values
    l1 = 1.0
    lmom_ratios = [l1, mean_tr * l1, mean_tr3, 0.0]
    
    print(f"\nFitting distributions with L-moment ratios: {lmom_ratios}")
    
    # Test the best performing distributions
    distributions = {
        'KAPPA': distr.kap,
        'GEV': distr.gev,
        'GLO': distr.glo,
        'GPA': distr.gpa,
        'GNO': distr.gno
    }
    
    fitted_params = {}
    
    for name, dist in distributions.items():
        try:
            params = dist.lmom_fit(lmom_ratios=lmom_ratios)
            fitted_params[name] = params
            print(f"\n{name} Parameters: {params}")
            
            # Generate 1000 random samples
            samples = dist.rvs(**params, size=1000)
            
            print(f"  Sample statistics:")
            print(f"    Mean: {np.mean(samples):.4f}")
            print(f"    Std:  {np.std(samples):.4f}")
            print(f"    Min:  {np.min(samples):.4f}")
            print(f"    Max:  {np.max(samples):.4f}")
            
        except Exception as e:
            print(f"\nError with {name}: {e}")
    
    # Demonstrate quantile calculations
    print(f"\n{'='*50}")
    print("QUANTILE CALCULATIONS")
    print(f"{'='*50}")
    
    probabilities = [0.01, 0.05, 0.1, 0.25, 0.5, 0.75, 0.9, 0.95, 0.99]
    
    for name, params in fitted_params.items():
        print(f"\n{name} Distribution Quantiles:")
        dist = distributions[name]
        quantiles = [dist.ppf(p, **params) for p in probabilities]
        
        for p, q in zip(probabilities, quantiles):
            print(f"  P({p:4.2f}) = {q:8.4f}")
    
    # Test individual tr, tr3 pairs
    print(f"\n{'='*50}")
    print("INDIVIDUAL PAIR FITTING (KAPPA)")
    print(f"{'='*50}")
    
    print("Fitting KAPPA distribution to each individual tr, tr3 pair:")
    print("Pair | TR     | TR3     | k        | h        | loc      | scale")
    print("-" * 70)
    
    for i, (tr, tr3) in enumerate(zip(tr_values[:10], tr3_values[:10]), 1):
        lmom_i = [1.0, tr, tr3, 0.0]
        try:
            params_i = distr.kap.lmom_fit(lmom_ratios=lmom_i)
            print(f"{i:4d} | {tr:6.4f} | {tr3:7.4f} | {params_i['k']:8.4f} | "
                  f"{params_i['h']:8.4f} | {params_i['loc']:8.4f} | {params_i['scale']:8.4f}")
        except Exception as e:
            print(f"{i:4d} | {tr:6.4f} | {tr3:7.4f} | Error: {str(e)}")

if __name__ == "__main__":
    demonstrate_sampling()
