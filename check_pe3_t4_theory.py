#!/usr/bin/env python3
"""
Check what T4 the PE3 distribution theoretically produces
"""

import numpy as np
from lmoments3 import distr

def check_pe3_t4():
    """Check theoretical T4 for PE3"""
    
    print("CHECKING PE3 THEORETICAL T4")
    print("=" * 40)
    
    # Book values
    tau2_book = 0.1103
    tau3_book = 0.0279
    tau4_book = 0.1366
    
    print(f"Book values:")
    print(f"  τ₂ = {tau2_book}")
    print(f"  τ₃ = {tau3_book}")
    print(f"  τ₄ = {tau4_book}")
    
    # Fit PE3 with book's first 3 L-moment ratios
    try:
        pe3_params = distr.pe3.lmom_fit(lmom_ratios=[1.0, tau2_book, tau3_book, 0.0])
        print(f"\nPE3 fitted parameters:")
        print(f"  μ = {pe3_params['loc']:.6f}")
        print(f"  σ = {pe3_params['scale']:.6f}")
        print(f"  γ = {pe3_params['skew']:.6f}")
        
        # Try to get theoretical L-moments with nmom=3
        try:
            theo_lmoms_3 = distr.pe3.lmom_ratios(nmom=3, **pe3_params)
            print(f"\nTheoretical L-moments (nmom=3):")
            print(f"  λ₁ = {theo_lmoms_3[0]:.6f}")
            print(f"  τ₂ = {theo_lmoms_3[1]:.6f}")
            print(f"  τ₃ = {theo_lmoms_3[2]:.6f}")
        except Exception as e:
            print(f"Error with nmom=3: {e}")
        
        # Try to get theoretical L-moments with nmom=4
        try:
            theo_lmoms_4 = distr.pe3.lmom_ratios(nmom=4, **pe3_params)
            print(f"\nTheoretical L-moments (nmom=4):")
            print(f"  λ₁ = {theo_lmoms_4[0]:.6f}")
            print(f"  τ₂ = {theo_lmoms_4[1]:.6f}")
            print(f"  τ₃ = {theo_lmoms_4[2]:.6f}")
            print(f"  τ₄ = {theo_lmoms_4[3]:.6f}")
            
            print(f"\nComparison with book τ₄:")
            print(f"  Book τ₄: {tau4_book:.4f}")
            print(f"  PE3 theoretical τ₄: {theo_lmoms_4[3]:.6f}")
            print(f"  Difference: {abs(theo_lmoms_4[3] - tau4_book):.6f}")
            
            if abs(theo_lmoms_4[3] - tau4_book) > 0.01:
                print(f"  *** LARGE DIFFERENCE! PE3 cannot match book's τ₄ ***")
            else:
                print(f"  Good match!")
                
        except Exception as e:
            print(f"Error with nmom=4: {e}")
            print("PE3 may not support τ₄ calculation in lmoments3")
        
    except Exception as e:
        print(f"Error fitting PE3: {e}")
    
    # Test other distributions that might match all 4 L-moment ratios
    print(f"\n{'='*50}")
    print("TESTING OTHER DISTRIBUTIONS WITH T4=0.1366")
    print(f"{'='*50}")
    
    distributions = ['gev', 'glo', 'gpa', 'gno', 'kap']
    
    for dist_name in distributions:
        try:
            dist = getattr(distr, dist_name)
            params = dist.lmom_fit(lmom_ratios=[1.0, tau2_book, tau3_book, tau4_book])
            
            # Check theoretical L-moments
            theo_lmoms = dist.lmom_ratios(nmom=4, **params)
            
            print(f"\n{dist_name.upper()}:")
            print(f"  Fitted parameters: {params}")
            print(f"  Theoretical τ₄: {theo_lmoms[3]:.6f}")
            print(f"  τ₄ error: {abs(theo_lmoms[3] - tau4_book):.6f}")
            
            if abs(theo_lmoms[3] - tau4_book) < 0.001:
                print(f"  *** EXCELLENT τ₄ MATCH! ***")
                
                # Check if this distribution also gives gamma ≈ 0.1626
                if 'skew' in params:
                    gamma = params['skew']
                elif 'kappa' in params:
                    gamma = params['kappa']  # For some distributions
                else:
                    gamma = None
                
                if gamma is not None:
                    print(f"  Shape parameter: {gamma:.6f}")
                    if abs(gamma - 0.1626) < 0.01:
                        print(f"  *** MIGHT EXPLAIN BOOK'S GAMMA! ***")
            
        except Exception as e:
            print(f"\n{dist_name.upper()}: Error - {e}")
    
    print(f"\n{'='*50}")
    print("CONCLUSION")
    print(f"{'='*50}")
    
    print("Key insights:")
    print("• PE3 is a 3-parameter distribution - τ₄ is determined by τ₂ and τ₃")
    print("• Book's τ₄ = 0.1366 may not be consistent with PE3 distribution")
    print("• The book might have used a 4-parameter distribution")
    print("• Or the book's τ₄ might be from empirical data, not theoretical")
    print("")
    print("Next steps:")
    print("• Check if any 4-parameter distribution matches all book values")
    print("• Verify if book's L-moment ratios are internally consistent")

if __name__ == "__main__":
    check_pe3_t4()
