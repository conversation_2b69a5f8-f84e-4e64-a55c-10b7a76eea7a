#!/usr/bin/env python3
"""
Python wrapper to run the lmomco R script and compare results with lmoments3.
"""

import subprocess
import sys
import os

def run_r_script():
    """Run the R script and capture output"""
    
    print("Running lmomco R package test...")
    print("=" * 50)
    
    try:
        # Try to find Rscript in common locations
        rscript_paths = [
            'Rscript',  # If in PATH
            r'C:\Program Files\R\R-4.3.0\bin\x64\Rscript.exe',
            r'C:\Program Files\R\R-4.3.0\bin\Rscript.exe'
        ]

        result = None
        for rscript_path in rscript_paths:
            try:
                result = subprocess.run(
                    [rscript_path, 'test_lmomco.R'],
                    capture_output=True,
                    text=True,
                    cwd=os.getcwd()
                )
                break
            except FileNotFoundError:
                continue

        if result is None:
            raise FileNotFoundError("Rscript not found in any common location")
        
        if result.returncode == 0:
            print("R script executed successfully!\n")
            print("OUTPUT:")
            print("-" * 30)
            print(result.stdout)
            
            if result.stderr:
                print("\nWARNINGS/MESSAGES:")
                print("-" * 30)
                print(result.stderr)
                
        else:
            print(f"R script failed with return code: {result.returncode}")
            print("\nSTDOUT:")
            print(result.stdout)
            print("\nSTDERR:")
            print(result.stderr)
            
    except FileNotFoundError:
        print("Error: Rscript not found. Please make sure R is installed and in your PATH.")
        print("\nTo install R:")
        print("1. Download R from https://cran.r-project.org/")
        print("2. Install R and make sure it's added to your PATH")
        print("3. Restart your terminal/command prompt")
        return False
        
    except Exception as e:
        print(f"Error running R script: {e}")
        return False
    
    return True

def compare_with_python():
    """Compare results with the Python lmoments3 implementation"""
    
    print("\n" + "=" * 60)
    print("COMPARISON WITH PYTHON lmoments3")
    print("=" * 60)
    
    # Import our Python test results
    try:
        import numpy as np
        from lmoments3 import distr
        
        # Your data
        tr_values = [.1209, .0915, .1124, .1032, .0967, .1328, .1008, .1143, .1107, .1179, 
                     .1308, .1119, .1018, .1025, .1054, .1174, .1115, .1003, .1046]
        tr3_values = [.0488, .0105, .0614, .0417, -.0134, -.0176, .0943, .0555, .0478, .0492, 
                      .0940, -.0429, .0435, .0182, -.0224, .0124, -.0346, .0446, .0128]
        rec_length = [98, 59, 90, 61, 65, 86, 78, 72, 67, 99, 49, 61, 69, 73, 70, 66, 59, 74, 82]
        
        # Calculate weighted regional L-moment ratios
        tr_array = np.array(tr_values)
        tr3_array = np.array(tr3_values)
        rec_array = np.array(rec_length)
        
        regional_tr = (tr_array * rec_array).sum() / rec_array.sum()
        regional_tr3 = (tr3_array * rec_array).sum() / rec_array.sum()
        
        print(f"Python lmoments3 results:")
        print(f"  Regional TR:  {regional_tr:.6f}")
        print(f"  Regional TR3: {regional_tr3:.6f}")
        
        # Fit KAPPA distribution using lmoments3
        l1 = 1.0
        lmom_ratios = [l1, regional_tr * l1, regional_tr3, 0.0]
        
        kappa_params = distr.kap.lmom_fit(lmom_ratios=lmom_ratios)
        print(f"\nPython KAPPA parameters:")
        for key, value in kappa_params.items():
            print(f"  {key}: {value:.6f}")
        
        # Calculate theoretical L-moments
        theo_lmoms = distr.kap.lmom_ratios(**kappa_params)
        print(f"\nPython theoretical L-moments:")
        print(f"  L1: {theo_lmoms[0]:.6f}")
        print(f"  TR: {theo_lmoms[1]:.6f}")
        print(f"  T3: {theo_lmoms[2]:.6f}")
        print(f"  T4: {theo_lmoms[3]:.6f}")
        
        print(f"\nBoth implementations should give very similar results!")
        print(f"Small differences may be due to numerical precision or parameter conventions.")
        
    except ImportError:
        print("lmoments3 not available for comparison")
    except Exception as e:
        print(f"Error in Python comparison: {e}")

if __name__ == "__main__":
    success = run_r_script()
    
    if success:
        compare_with_python()
    else:
        print("\nFailed to run R script. Please check R installation.")
        sys.exit(1)
