#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Cluster Remapping Script

This script remaps cluster assignments from 1D format back to 2D grid format,
preserving spatial relationships and handling masked regions appropriately.

Usage:
    python src/models/run_cluster_remapping.py --input CI_results.nc [options]

The script will:
1. Load the CI_results.nc file containing 1D cluster assignments
2. Set up the standardizer with the same configuration used during clustering
3. Remap each k=N variable from 1D to 2D grid format
4. Save the remapped data as CI_results_remap.nc (or custom name)
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# Add the project root to the path for imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from src.models.remap_clusters import remap_clusters_file
from src.utils.logging_config import setup_logging

logger = logging.getLogger(__name__)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Remap cluster assignments from 1D to 2D grid format",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Basic usage - remap CI_results.nc to CI_results_remap.nc
    python src/models/run_cluster_remapping.py --input CI_results.nc
    
    # Specify custom output suffix
    python src/models/run_cluster_remapping.py --input CI_results.nc --suffix "_2D"
    
    # Specify custom stats file location
    python src/models/run_cluster_remapping.py --input CI_results.nc --stats-file data/stats.nc
    
    # Enable debug logging
    python src/models/run_cluster_remapping.py --input CI_results.nc --log-level DEBUG
        """
    )
    
    parser.add_argument(
        "--input", "-i",
        type=str,
        required=True,
        help="Path to CI_results.nc file containing 1D cluster assignments"
    )
    
    parser.add_argument(
        "--stats-file", "-s",
        type=str,
        default=None,
        help="Path to cluster_precip_stats.nc file (default: cluster_precip_stats.nc)"
    )
    
    parser.add_argument(
        "--suffix",
        type=str,
        default="_remap",
        help="Suffix to add to output filename (default: _remap)"
    )
    
    parser.add_argument(
        "--log-level",
        type=str,
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Logging level (default: INFO)"
    )
    
    return parser.parse_args()


def validate_inputs(args):
    """
    Validate input arguments and files.
    
    Parameters:
    -----------
    args : argparse.Namespace
        Parsed command line arguments
        
    Raises:
    -------
    FileNotFoundError
        If required input files are not found
    ValueError
        If arguments are invalid
    """
    # Check input file exists
    if not os.path.exists(args.input):
        raise FileNotFoundError(f"Input file not found: {args.input}")
    
    # Check input file has correct extension
    if not args.input.endswith('.nc'):
        raise ValueError(f"Input file must be a NetCDF file (.nc): {args.input}")
    
    # Check stats file if specified
    if args.stats_file and not os.path.exists(args.stats_file):
        raise FileNotFoundError(f"Stats file not found: {args.stats_file}")
    
    # Validate suffix
    if not args.suffix:
        raise ValueError("Output suffix cannot be empty")
    
    logger.info("Input validation passed")


def main():
    """Main function to run cluster remapping."""
    # Parse command line arguments
    args = parse_args()
    
    # Setup logging
    log_level = getattr(logging, args.log_level)
    setup_logging(log_level=log_level)
    
    logger.info("Starting cluster remapping process")
    logger.info(f"Input file: {args.input}")
    logger.info(f"Stats file: {args.stats_file or 'default (cluster_precip_stats.nc)'}")
    logger.info(f"Output suffix: {args.suffix}")
    
    try:
        # Validate inputs
        validate_inputs(args)
        
        # Perform cluster remapping
        logger.info("Beginning cluster remapping...")
        output_path = remap_clusters_file(
            ci_results_path=args.input,
            stats_file_path=args.stats_file,
            output_suffix=args.suffix
        )
        
        logger.info("Cluster remapping completed successfully!")
        logger.info(f"Output saved to: {output_path}")
        
        # Verify output file was created
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
            logger.info(f"Output file size: {file_size:.2f} MB")
        else:
            logger.error("Output file was not created successfully")
            sys.exit(1)
            
    except FileNotFoundError as e:
        logger.error(f"File not found: {e}")
        sys.exit(1)
        
    except ValueError as e:
        logger.error(f"Invalid input: {e}")
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"Unexpected error during cluster remapping: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
