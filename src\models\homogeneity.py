# -*- coding: utf-8 -*-
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from lmoments3 import distr, lmom_ratios
import os
import logging

logger = logging.getLogger(__name__)

class HomogeneityAnalysis:
    """
    Class for analyzing homogeneity of clusters.
    """
    def __init__(self, cluster_data, cluster_labels, cluster_range, outdir='homogeneity_results', n_sim=500):
        """
        Initialize the homogeneity analyzer.
        
        Parameters:
        -----------
        cluster_data : numpy.ndarray
            Input data matrix (n_samples, n_features)
        cluster_labels : dict
            Dictionary of {k: array of cluster labels of size n_samples} for different k
        cluster_range : list or range
            List or range of k values to analyze
        outdir : str, optional
            Directory to save results
        n_sim : int, optional
            Number of simulations
        """
        self.data = cluster_data
        self.labels_dict = cluster_labels
        self.k_values = cluster_range
        self.outdir = outdir
        self.n_sim = n_sim
        self.results = {}
        self.df = None
        
        os.makedirs(outdir, exist_ok=True)
        logger.info(f"Initialized HomogeneityAnalysis with {len(cluster_range)} k values")

    def calculate_l_moments(self, data):
        """
        Calculate L-moments for a data series.
        
        Parameters:
        -----------
        data : numpy.ndarray
            Input data series
            
        Returns:
        --------
        list
            L-moment ratios
        """
        lmr = lmom_ratios(data, nmom=4)
        return lmr

    def simulate_kappa(self, data, size):
        """
        Simulate data from a Kappa distribution fitted to the input data.
        
        Parameters:
        -----------
        data : numpy.ndarray
            Input data series
        size : int
            Number of samples to generate
            
        Returns:
        --------
        numpy.ndarray
            Simulated data
        """
        try:
            kappa_params = distr.kap.lmom_fit(data)
            fitted_kappa = distr.kap(**kappa_params)
            simulated = fitted_kappa.rvs(size=size)
        except (ValueError, TypeError):
            # Fall back to simple resampling with replacement if Kappa fails
            logger.warning("Kappa distribution fitting failed, using resampling instead")
            simulated = np.random.choice(data, size=size, replace=True)
        return simulated

    def compute_homogeneity_statistics(self):
        """
        Compute homogeneity statistics for all cluster counts.
        """
        for k in self.k_values:
            logger.info(f'Processing k={k}...')
            labels = self.labels_dict[k]
            clusters = np.unique(labels)
            v1, v2, v3 = [], [], []
            sim_stats = {'v1': [], 'v2': [], 'v3': []}
            
            for cluster_id in clusters:
                cluster_series = self.data[labels == cluster_id]
                if cluster_series.shape[0] < 3:
                    logger.warning(f"Skipping cluster {cluster_id} with fewer than 3 samples")
                    continue
                
                lcv = []
                lca = []
                lcs = []
                for series in cluster_series:
                    lmr = self.calculate_l_moments(series)
                    lcv.append(lmr[1])
                    lca.append(lmr[2])
                    lcs.append(lmr[3])
                lcv = np.array(lcv)
                lca = np.array(lca)
                lcs = np.array(lcs)

                # V statistics
                v1.append(np.std(lcv) / np.mean(lcv))
                v2.append(np.std(lca))
                v3.append(np.std(lcs))

                # Simulations
                for _ in range(self.n_sim):
                    simulated_lcv = []
                    simulated_lca = []
                    simulated_lcs = []
                    for series in cluster_series:
                        sim_data = self.simulate_kappa(series, len(series))
                        sim_lmr = self.calculate_l_moments(sim_data)
                        simulated_lcv.append(sim_lmr[1])
                        simulated_lca.append(sim_lmr[2])
                        simulated_lcs.append(sim_lmr[3])
                    sim_stats['v1'].append(np.std(simulated_lcv) / np.mean(simulated_lcv))
                    sim_stats['v2'].append(np.std(simulated_lca))
                    sim_stats['v3'].append(np.std(simulated_lcs))

            v1_avg = np.mean(v1)
            v2_avg = np.mean(v2)
            v3_avg = np.mean(v3)
            sim_v1_mean = np.mean(sim_stats['v1'])
            sim_v2_mean = np.mean(sim_stats['v2'])
            sim_v3_mean = np.mean(sim_stats['v3'])

            # H statistics
            h1 = v1_avg / sim_v1_mean
            h2 = v2_avg / sim_v2_mean
            h3 = v3_avg / sim_v3_mean

            self.results[k] = {
                'V1': v1_avg,
                'V2': v2_avg,
                'V3': v3_avg,
                'H1': h1,
                'H2': h2,
                'H3': h3
            }
            logger.info(f"k={k}: H1={h1:.4f}, H2={h2:.4f}, H3={h3:.4f}")

        self._save_results()

    def _save_results(self):
        """
        Save homogeneity results to CSV.
        """
        df = pd.DataFrame.from_dict(self.results, orient='index')
        self.df = df
        output_path = os.path.join(self.outdir, 'homogeneity_scores.csv')
        df.to_csv(output_path)
        logger.info(f"Saved homogeneity scores to {output_path}")

    def plot_homogeneity_statistics(self):
        """
        Plot homogeneity statistics.
        """
        if self.df is None:
            raise RuntimeError("No results available. Run compute_homogeneity_statistics first.")
            
        df = self.df
        x = list(df.index.to_numpy())
        fig, axes = plt.subplots(1, 3, figsize=(15, 5), sharex=True)

        for i, stat in enumerate(['H1', 'H2', 'H3']):
            axes[i].plot(x, df[stat].values, marker='o', label=stat)
            axes[i].axhspan(0, 1, facecolor='green', alpha=0.2, label='Homogeneous' if i == 0 else "")
            axes[i].axhspan(1, 2, facecolor='yellow', alpha=0.2, label='Possibly Heterogeneous' if i == 0 else "")
            axes[i].axhspan(2, df[stat].max() + 0.5, facecolor='red', alpha=0.2, label='Heterogeneous' if i == 0 else "")
            axes[i].set_title(f'{stat} by Cluster Count')
            axes[i].set_xlabel('Number of Clusters')
            axes[i].grid(True)
            axes[i].set_ylabel('Statistic Value')
        axes[0].legend(loc='upper left')
        plt.suptitle('Hosking-Wallis Homogeneity Statistics (H1, H2, H3)')
        plt.tight_layout(rect=[0, 0, 0.95, 0.95])
        
        output_path = os.path.join(self.outdir, 'homogeneity_plot.png')
        plt.savefig(output_path, dpi=300)
        plt.close(fig)
        logger.info(f"Saved homogeneity plot to {output_path}")

