#!/usr/bin/env python3
"""
Test script for the updated precipitation analyzer with atmospheric variables.

This script demonstrates how to use the enhanced PrecipitationAnalyzer class
that now includes calculations for h500, mslp, and uv850 atmospheric variables.
"""

import os
import sys
import numpy as np
import xarray as xr
import tempfile
import logging

# Add src to path
sys.path.insert(0, 'src')

from models.precipitation import PrecipitationAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data():
    """Create synthetic test data for demonstration."""
    
    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    logger.info(f"Creating test data in: {temp_dir}")
    
    # Grid parameters
    ny, nx = 10, 15
    n_days = 1095  # 3 years
    n_clusters = 5
    
    # Create sample_data directory
    sample_data_dir = os.path.join(temp_dir, "sample_data")
    os.makedirs(sample_data_dir, exist_ok=True)
    
    # Create coordinates
    lat_coords = np.linspace(35, 45, ny)
    lon_coords = np.linspace(-120, -110, nx)
    time_coords = np.arange(n_days)
    
    # Create precipitation data
    np.random.seed(42)
    precip_data = np.random.gamma(2, 3, (n_days, ny, nx))
    
    precip_ds = xr.Dataset(
        {"precip": (["time", "y", "x"], precip_data)},
        coords={"time": time_coords, "y": lat_coords, "x": lon_coords}
    )
    precip_path = os.path.join(temp_dir, "precip.nc")
    precip_ds.to_netcdf(precip_path)
    
    # Create cluster data
    cluster_data = {}
    for k in range(2, 6):
        cluster_assignments = np.random.randint(0, k, n_days)
        cluster_data[f"k={k}"] = (["time"], cluster_assignments)
    
    cluster_ds = xr.Dataset(cluster_data, coords={"time": time_coords})
    cluster_path = os.path.join(temp_dir, "clusters.nc")
    cluster_ds.to_netcdf(cluster_path)
    
    # Create atmospheric data files
    # H500 (geopotential height at 500 hPa)
    h500_data = np.random.normal(5500, 100, (n_days, ny, nx))  # Typical 500 hPa heights
    h500_ds = xr.Dataset(
        {"z": (["time", "pressure_level", "latitude", "longitude"], h500_data[:, np.newaxis, :, :])},
        coords={
            "time": time_coords,
            "pressure_level": [500],
            "latitude": lat_coords,
            "longitude": lon_coords
        }
    )
    h500_path = os.path.join(sample_data_dir, "h_500_CONUS_1979_2020.nc")
    h500_ds.to_netcdf(h500_path)
    
    # MSLP (mean sea level pressure)
    mslp_data = np.random.normal(101325, 1000, (n_days, ny, nx))  # Typical MSLP values in Pa
    mslp_ds = xr.Dataset(
        {"msl": (["time", "latitude", "longitude"], mslp_data)},
        coords={"time": time_coords, "latitude": lat_coords, "longitude": lon_coords}
    )
    mslp_path = os.path.join(sample_data_dir, "era5_mslp_CONUS_1979_2020.nc")
    mslp_ds.to_netcdf(mslp_path)
    
    # U850 (zonal wind at 850 hPa)
    u850_data = np.random.normal(0, 10, (n_days, ny, nx))  # Typical wind speeds
    u850_ds = xr.Dataset(
        {"u": (["time", "pressure_level", "latitude", "longitude"], u850_data[:, np.newaxis, :, :])},
        coords={
            "time": time_coords,
            "pressure_level": [850],
            "latitude": lat_coords,
            "longitude": lon_coords
        }
    )
    u850_path = os.path.join(sample_data_dir, "u_850_CONUS_1979_2020.nc")
    u850_ds.to_netcdf(u850_path)
    
    # V850 (meridional wind at 850 hPa)
    v850_data = np.random.normal(0, 10, (n_days, ny, nx))  # Typical wind speeds
    v850_ds = xr.Dataset(
        {"v": (["time", "pressure_level", "latitude", "longitude"], v850_data[:, np.newaxis, :, :])},
        coords={
            "time": time_coords,
            "pressure_level": [850],
            "latitude": lat_coords,
            "longitude": lon_coords
        }
    )
    v850_path = os.path.join(sample_data_dir, "v_850_CONUS_1979_2020.nc")
    v850_ds.to_netcdf(v850_path)
    
    return temp_dir, precip_path, cluster_path

def test_atmospheric_calculations():
    """Test the atmospheric variable calculations."""
    
    logger.info("Creating test data...")
    temp_dir, precip_path, cluster_path = create_test_data()
    
    try:
        # Initialize analyzer with atmospheric data directory
        output_path = os.path.join(temp_dir, "output_with_atmos.nc")
        
        logger.info("Initializing PrecipitationAnalyzer...")
        analyzer = PrecipitationAnalyzer(
            precip_path=precip_path,
            cluster_path=cluster_path,
            output_path=output_path,
            max_k=5,
            atmos_data_dir=os.path.join(temp_dir, "sample_data")
        )
        
        # Run the analysis
        logger.info("Running precipitation and atmospheric analysis...")
        analyzer.run()
        
        # Check the output
        logger.info("Checking output file...")
        output_ds = xr.open_dataset(output_path)
        
        logger.info(f"Output variables: {list(output_ds.data_vars.keys())}")
        logger.info(f"Output dimensions: {dict(output_ds.dims)}")
        
        # Check for atmospheric variables
        expected_atmos_vars = ['mean_h500', 'anomaly_h500', 'mean_mslp', 'anomaly_mslp', 
                              'mean_u850', 'anomaly_u850', 'mean_v850', 'anomaly_v850']
        
        found_atmos_vars = [var for var in expected_atmos_vars if var in output_ds.data_vars]
        logger.info(f"Found atmospheric variables: {found_atmos_vars}")
        
        if found_atmos_vars:
            logger.info("✅ SUCCESS: Atmospheric variables were calculated and saved!")
            
            # Show some sample values
            for var in found_atmos_vars[:2]:  # Show first 2 variables
                sample_data = output_ds[var].isel(k=2, cluster=0).values
                logger.info(f"Sample {var} values (k=2, cluster=0): min={np.nanmin(sample_data):.2f}, max={np.nanmax(sample_data):.2f}")
        else:
            logger.warning("⚠️  No atmospheric variables found in output")
            
        output_ds.close()
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise
    finally:
        # Clean up
        import shutil
        shutil.rmtree(temp_dir)
        logger.info("Cleaned up test data")

if __name__ == "__main__":
    test_atmospheric_calculations()
