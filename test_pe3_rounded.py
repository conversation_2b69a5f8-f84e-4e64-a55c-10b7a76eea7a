#!/usr/bin/env python3
"""
Test PE3 fitting with all calculations rounded to 4 decimal places.
"""

import numpy as np
from lmoments3 import distr

def test_pe3_with_rounding():
    """Test PE3 with 4-decimal rounding at each step"""
    
    print("PE3 FITTING WITH 4-DECIMAL ROUNDING")
    print("=" * 50)
    
    # Your data
    tr_values = [.1209, .0915, .1124, .1032, .0967, .1328, .1008, .1143, .1107, .1179, 
                 .1308, .1119, .1018, .1025, .1054, .1174, .1115, .1003, .1046]
    
    tr3_values = [.0488, .0105, .0614, .0417, -.0134, -.0176, .0943, .0555, .0478, .0492, 
                  .0940, -.0429, .0435, .0182, -.0224, .0124, -.0346, .0446, .0128]
    
    rec_length = [98, 59, 90, 61, 65, 86, 78, 72, 67, 99, 49, 61, 69, 73, 70, 66, 59, 74, 82]
    
    print("Original data (as provided):")
    print(f"  TR values: {tr_values}")
    print(f"  TR3 values: {tr3_values}")
    print(f"  Record lengths: {rec_length}")
    
    # Convert to numpy arrays
    tr_array = np.array(tr_values)
    tr3_array = np.array(tr3_values)
    n_array = np.array(rec_length)
    
    print(f"\nStep 1: Calculate weighted sums")
    
    # Calculate numerators with full precision first
    tr_numerator_full = (n_array * tr_array).sum()
    tr3_numerator_full = (n_array * tr3_array).sum()
    denominator_full = n_array.sum()
    
    print(f"  TR numerator (full precision): {tr_numerator_full}")
    print(f"  TR3 numerator (full precision): {tr3_numerator_full}")
    print(f"  Denominator: {denominator_full}")
    
    # Method 1: Round the final ratios to 4 decimals
    print(f"\nMethod 1: Round final ratios to 4 decimals")
    regional_tr_method1 = round(tr_numerator_full / denominator_full, 4)
    regional_tr3_method1 = round(tr3_numerator_full / denominator_full, 4)
    
    print(f"  Regional TR: {regional_tr_method1}")
    print(f"  Regional TR3: {regional_tr3_method1}")
    
    # Method 2: Round individual products before summing
    print(f"\nMethod 2: Round individual products before summing")
    tr_products_rounded = [round(n * tr, 4) for n, tr in zip(n_array, tr_array)]
    tr3_products_rounded = [round(n * tr3, 4) for n, tr3 in zip(n_array, tr3_array)]
    
    print(f"  TR products (rounded): {tr_products_rounded}")
    print(f"  TR3 products (rounded): {tr3_products_rounded}")
    
    tr_numerator_method2 = sum(tr_products_rounded)
    tr3_numerator_method2 = sum(tr3_products_rounded)
    
    regional_tr_method2 = tr_numerator_method2 / denominator_full
    regional_tr3_method2 = tr3_numerator_method2 / denominator_full
    
    print(f"  TR numerator (rounded products): {tr_numerator_method2}")
    print(f"  TR3 numerator (rounded products): {tr3_numerator_method2}")
    print(f"  Regional TR: {regional_tr_method2:.6f}")
    print(f"  Regional TR3: {regional_tr3_method2:.6f}")
    
    # Method 3: Round input data to 4 decimals first
    print(f"\nMethod 3: Round input data to 4 decimals first")
    tr_rounded = [round(tr, 4) for tr in tr_values]
    tr3_rounded = [round(tr3, 4) for tr3 in tr3_values]
    
    print(f"  TR values (rounded): {tr_rounded}")
    print(f"  TR3 values (rounded): {tr3_rounded}")
    
    tr_array_rounded = np.array(tr_rounded)
    tr3_array_rounded = np.array(tr3_rounded)
    
    regional_tr_method3 = (n_array * tr_array_rounded).sum() / n_array.sum()
    regional_tr3_method3 = (n_array * tr3_array_rounded).sum() / n_array.sum()
    
    print(f"  Regional TR: {regional_tr_method3:.6f}")
    print(f"  Regional TR3: {regional_tr3_method3:.6f}")
    
    # Method 4: Round everything to 4 decimals at each step
    print(f"\nMethod 4: Round everything to 4 decimals at each step")
    tr_products_4dec = [round(n * round(tr, 4), 4) for n, tr in zip(n_array, tr_array)]
    tr3_products_4dec = [round(n * round(tr3, 4), 4) for n, tr3 in zip(n_array, tr3_array)]
    
    tr_sum_4dec = round(sum(tr_products_4dec), 4)
    tr3_sum_4dec = round(sum(tr3_products_4dec), 4)
    
    regional_tr_method4 = round(tr_sum_4dec / denominator_full, 4)
    regional_tr3_method4 = round(tr3_sum_4dec / denominator_full, 4)
    
    print(f"  Regional TR: {regional_tr_method4}")
    print(f"  Regional TR3: {regional_tr3_method4}")
    
    # Test PE3 fitting with each method
    methods = [
        ("Method 1 (round final)", regional_tr_method1, regional_tr3_method1),
        ("Method 2 (round products)", regional_tr_method2, regional_tr3_method2),
        ("Method 3 (round inputs)", regional_tr_method3, regional_tr3_method3),
        ("Method 4 (round all steps)", regional_tr_method4, regional_tr3_method4)
    ]
    
    print(f"\n{'='*60}")
    print("PE3 FITTING RESULTS")
    print(f"{'='*60}")
    
    for method_name, tr_val, tr3_val in methods:
        print(f"\n{method_name}:")
        print(f"  TR: {tr_val:.6f}")
        print(f"  TR3: {tr3_val:.6f}")
        
        try:
            l1 = 1.0
            lmom_ratios = [l1, tr_val * l1, tr3_val, 0.0]
            pe3_params = distr.pe3.lmom_fit(lmom_ratios=lmom_ratios)
            
            gamma = pe3_params['skew']
            print(f"  PE3 gamma: {gamma:.6f}")
            print(f"  Difference from book (0.1626): {abs(gamma - 0.1626):.6f}")
            
            if abs(gamma - 0.1626) < 0.001:
                print(f"  *** CLOSE MATCH TO BOOK VALUE! ***")
            
        except Exception as e:
            print(f"  Error: {e}")
    
    # Additional test: Try different rounding levels
    print(f"\n{'='*60}")
    print("TESTING DIFFERENT ROUNDING LEVELS")
    print(f"{'='*60}")
    
    for decimals in [2, 3, 4, 5]:
        tr_round = round(tr_numerator_full / denominator_full, decimals)
        tr3_round = round(tr3_numerator_full / denominator_full, decimals)
        
        try:
            lmom_ratios = [1.0, tr_round, tr3_round, 0.0]
            pe3_params = distr.pe3.lmom_fit(lmom_ratios=lmom_ratios)
            gamma = pe3_params['skew']
            
            print(f"{decimals} decimals: TR={tr_round:.{decimals}f}, TR3={tr3_round:.{decimals}f} -> gamma={gamma:.6f} (diff={abs(gamma-0.1626):.6f})")
            
        except Exception as e:
            print(f"{decimals} decimals: Error - {e}")
    
    # Test with book's exact values if we can find them
    print(f"\n{'='*60}")
    print("TESTING WITH POTENTIAL BOOK VALUES")
    print(f"{'='*60}")
    
    # Try values that might give gamma = 0.1626
    test_cases = [
        (0.1103, 0.0279),  # Rounded to 4 decimals
        (0.110, 0.028),    # Rounded to 3 decimals
        (0.1103, 0.0264),  # TR rounded, TR3 adjusted
        (0.11, 0.027),     # Rounded to 2-3 decimals
    ]
    
    for tr_test, tr3_test in test_cases:
        try:
            lmom_ratios = [1.0, tr_test, tr3_test, 0.0]
            pe3_params = distr.pe3.lmom_fit(lmom_ratios=lmom_ratios)
            gamma = pe3_params['skew']
            
            print(f"TR={tr_test:.4f}, TR3={tr3_test:.4f} -> gamma={gamma:.6f} (diff={abs(gamma-0.1626):.6f})")
            
            if abs(gamma - 0.1626) < 0.001:
                print(f"  *** VERY CLOSE TO BOOK VALUE! ***")
                
        except Exception as e:
            print(f"TR={tr_test:.4f}, TR3={tr3_test:.4f} -> Error: {e}")

if __name__ == "__main__":
    test_pe3_with_rounding()
