
library(lmom)

# Regional L-moment ratios
regional_tr <- 0.110298
regional_tr3 <- 0.027859
regional_tr4 <- 0.136613

cat("R lmom Analysis\n")
cat("===============\n")
cat("Regional L-moment ratios:\n")
cat("  λ₁ᴿ = 1.0000\n")
cat("  τ₂ᴿ =", sprintf("%.6f", regional_tr), "\n")
cat("  τ₃ᴿ =", sprintf("%.6f", regional_tr3), "\n")
cat("  τ₄ᴿ =", sprintf("%.6f", regional_tr4), "\n\n")

# Create L-moments vectors
l1 <- 1.0
l2 <- regional_tr * l1
l3 <- regional_tr3 * l2
l4 <- regional_tr4 * l2  # Note: L4 = T4 * L2

lmoms_3param <- c(l1, l2, l3, 0.0)
lmoms_4param <- c(l1, l2, l3, l4)

cat("L-moments for 3-param fitting:", sprintf("%.6f", lmoms_3param), "\n")
cat("L-moments for 4-param fitting:", sprintf("%.6f", lmoms_4param), "\n\n")

# 3-parameter distributions
cat("3-parameter distributions:\n")
cat("=========================\n")

# GEV
tryCatch({
  gev_params <- pelgev(lmoms_3param)
  cat("\nGEV:\n")
  cat("  xi:", sprintf("%.6f", gev_params[1]), "\n")
  cat("  alpha:", sprintf("%.6f", gev_params[2]), "\n")
  cat("  kappa:", sprintf("%.6f", gev_params[3]), "\n")
}, error = function(e) {
  cat("\nGEV: Error -", e$message, "\n")
})

# GLO
tryCatch({
  glo_params <- pelglo(lmoms_3param)
  cat("\nGLO:\n")
  cat("  xi:", sprintf("%.6f", glo_params[1]), "\n")
  cat("  alpha:", sprintf("%.6f", glo_params[2]), "\n")
  cat("  kappa:", sprintf("%.6f", glo_params[3]), "\n")
}, error = function(e) {
  cat("\nGLO: Error -", e$message, "\n")
})

# GPA
tryCatch({
  gpa_params <- pelgpa(lmoms_3param)
  cat("\nGPA:\n")
  cat("  xi:", sprintf("%.6f", gpa_params[1]), "\n")
  cat("  alpha:", sprintf("%.6f", gpa_params[2]), "\n")
  cat("  kappa:", sprintf("%.6f", gpa_params[3]), "\n")
}, error = function(e) {
  cat("\nGPA: Error -", e$message, "\n")
})

# PE3
tryCatch({
  pe3_params <- pelpe3(lmoms_3param)
  cat("\nPE3:\n")
  cat("  mu:", sprintf("%.6f", pe3_params[1]), "\n")
  cat("  sigma:", sprintf("%.6f", pe3_params[2]), "\n")
  cat("  gamma:", sprintf("%.6f", pe3_params[3]), "\n")
}, error = function(e) {
  cat("\nPE3: Error -", e$message, "\n")
})

# GNO
tryCatch({
  gno_params <- pelgno(lmoms_3param)
  cat("\nGNO:\n")
  cat("  xi:", sprintf("%.6f", gno_params[1]), "\n")
  cat("  alpha:", sprintf("%.6f", gno_params[2]), "\n")
  cat("  kappa:", sprintf("%.6f", gno_params[3]), "\n")
}, error = function(e) {
  cat("\nGNO: Error -", e$message, "\n")
})

# Gumbel
tryCatch({
  gum_params <- pelgum(lmoms_3param)
  cat("\nGUM:\n")
  cat("  xi:", sprintf("%.6f", gum_params[1]), "\n")
  cat("  alpha:", sprintf("%.6f", gum_params[2]), "\n")
}, error = function(e) {
  cat("\nGUM: Error -", e$message, "\n")
})

# 4-parameter distributions
cat("\n\n4-parameter distributions:\n")
cat("=========================\n")

# KAPPA
tryCatch({
  kap_params <- pelkap(lmoms_4param)
  cat("\nKAPPA (4-param):\n")
  cat("  xi:", sprintf("%.6f", kap_params[1]), "\n")
  cat("  alpha:", sprintf("%.6f", kap_params[2]), "\n")
  cat("  kappa:", sprintf("%.6f", kap_params[3]), "\n")
  cat("  h:", sprintf("%.6f", kap_params[4]), "\n")
}, error = function(e) {
  cat("\nKAPPA: Error -", e$message, "\n")
})
