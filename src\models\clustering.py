# -*- coding: utf-8 -*-
import os
import numpy as np
import xarray as xr
import pandas as pd
import matplotlib.pyplot as plt
import logging
from .kmeans_utils import kmeans_ci, ar1rand
from .hierarchical_utils import hierarchical_clustering_simple, plot_dendrogram, run_hierarchical_with_linkage_matrix
import gc


logger = logging.getLogger(__name__)

class EOFClusteringAnalyzer:
    """
    Class for analyzing EOF-based clustering results with support for both K-means and hierarchical clustering.
    """
    def __init__(self, tmpu, outdir, maxclust=40, nsim=100, method='kmeans'):
        """
        Initialize the EOF clustering analyzer.

        Parameters:
        -----------
        tmpu : numpy.ndarray
            Input data matrix
        outdir : str
            Output directory
        maxclust : int, optional
            Maximum number of clusters to consider
        nsim : int, optional
            Number of simulations
        method : str, optional
            Clustering method ('kmeans' or 'hierarchical')
        """
        self.tmpu = tmpu
        self.outdir = outdir
        self.maxclust = maxclust
        self.nsim = nsim
        self.method = method.lower()
        self.nr, self.nc = tmpu.shape
        self.CI = None
        self.rsims = None
        self.CIcis = None
        self.citop = None
        self.cibot = None

        # Validate clustering method
        if self.method not in ['kmeans', 'hierarchical']:
            raise ValueError("Method must be either 'kmeans' or 'hierarchical'")

        if not os.path.exists(self.outdir):
            os.makedirs(self.outdir)
            logger.info(f"Created output directory: {self.outdir}")

        logger.info(f"Initialized clustering analyzer with method: {self.method}")

        # Estimate memory usage and warn if high
        estimated_memory_gb = (self.nr * self.nc * self.nsim * 8) / (1024**3)
        if estimated_memory_gb > 4:
            logger.warning(f"High memory usage estimated: {estimated_memory_gb:.1f} GB")
            logger.warning("Consider using run_memory_efficient() method or reducing nsim")

    def load_or_compute_ci(self):
        """
        Load or compute the classifiability index for different cluster counts.
        """
        ci_path = os.path.join(self.outdir, f'CI_results_{self.method}.nc')
        if os.path.exists(ci_path):
            logger.info(f"Loading existing CI results from {ci_path}")
            tmp = xr.open_dataset(ci_path)
            self.CI = tmp.CI
        else:
            logger.info(f"Computing CI for different cluster counts using {self.method}")
            k_over = np.zeros((self.nr, self.maxclust))
            ci_over = np.zeros(self.maxclust)

            # Choose clustering function based on method
            if self.method == 'kmeans':
                clustering_func = kmeans_ci
            else:  # hierarchical
                clustering_func = hierarchical_clustering_simple  # Use simple version without CI

            for n in range(2, self.maxclust + 1):
                logger.info(f'Computing k={n} using {self.method}')
                try:
                    # Use appropriate n_jobs based on method
                    n_jobs = 64 if self.method == 'kmeans' else 8
                    k, ci = clustering_func(self.tmpu, None, None, None, n, 100, n_jobs)
                    k_over[:, n - 1] = np.squeeze(k)
                    ci_over[n - 1] = ci
                except Exception as e:
                    logger.error(f'Clustering failed for k={n} using {self.method}: {e}')
                    if self.method == 'hierarchical':
                        logger.info(f'Falling back to K-means for k={n}')
                        k, ci = kmeans_ci(self.tmpu, None, None, None, n, 100, 64)
                        k_over[:, n - 1] = np.squeeze(k)
                        ci_over[n - 1] = ci
                    else:
                        # If K-means also fails, use random assignment
                        logger.warning(f'Using random assignment for k={n}')
                        k = np.random.randint(0, n, size=self.tmpu.shape[0])
                        ci = 0.0
                        k_over[:, n - 1] = k
                        ci_over[n - 1] = ci

            colnames = [f'k={i}' for i in range(1, self.maxclust + 1)]
            k_df = pd.DataFrame(k_over, columns=colnames)
            ci_df = pd.DataFrame(ci_over, columns=['CI'])
            k_df['CI'] = ci_df
            k_xr = k_df.to_xarray()
            k_xr.to_netcdf(ci_path)
            logger.info(f"Saved CI results to {ci_path}")
            self.CI = ci_df

            # Generate dendrogram for hierarchical clustering
            if self.method == 'hierarchical':
                self._generate_dendrogram()

    def _generate_dendrogram(self):
        """
        Generate dendrogram for hierarchical clustering.
        """
        try:
            logger.info("Generating dendrogram for hierarchical clustering")
            # Use a representative clustering run to get linkage matrix
            _, _, linkage_matrix = run_hierarchical_with_linkage_matrix(
                self.tmpu, self.tmpu, 2, self.nc, 0, return_linkage=True
            )
            dendrogram_path = os.path.join(self.outdir, 'dendrogram.png')
            plot_dendrogram(linkage_matrix, dendrogram_path, self.maxclust)
        except Exception as e:
            logger.warning(f"Could not generate dendrogram: {e}")

    def plot_ci(self):
        """
        Plot the classifiability index values.
        """
        fig, ax = plt.subplots()
        ax.plot(self.CI)
        ax.set_ylabel('CI')
        ax.set_xlabel('Cluster')
        ax.set_title(f'CI Values ({self.method.title()} Clustering)')
        ax.set_xlim(1, self.maxclust)
        output_path = os.path.join(self.outdir, f'plot_ci_{self.method}.png')
        plt.savefig(output_path)
        plt.close(fig)
        logger.info(f"Saved CI plot to {output_path}")

    def generate_red_noise(self, chunk_size=None):
        """
        Generate red noise simulations with memory optimization.

        Parameters:
        -----------
        chunk_size : int, optional
            Number of simulations to process at once (default: auto-detect)
        """
        random_path = os.path.join(self.outdir, 'random.nc')
        if os.path.exists(random_path):
            logger.info(f"Loading existing red noise simulations from {random_path}")
            tmp = xr.open_dataset(random_path)
            self.rsims = tmp.rsims.data
        else:
            logger.info('Creating random red noise series (memory-optimized)')

            # Auto-detect chunk size based on available memory
            if chunk_size is None:
                # Estimate memory per simulation
                sim_memory_mb = (self.nr * self.nc * 8) / (1024 * 1024)  # 8 bytes per float64
                available_memory_mb = 2000  # Conservative estimate (2GB)
                chunk_size = max(1, min(self.nsim, int(available_memory_mb / sim_memory_mb)))
                logger.info(f"Auto-detected chunk size: {chunk_size} simulations per chunk")

            # Process in chunks to avoid memory issues
            self.rsims = np.zeros((self.nr, self.nc, self.nsim))

            for chunk_start in range(0, self.nsim, chunk_size):
                chunk_end = min(chunk_start + chunk_size, self.nsim)
                chunk_nsim = chunk_end - chunk_start
                logger.info(f"Processing simulations {chunk_start+1}-{chunk_end} of {self.nsim}")

                # Process each variable in this chunk
                for i in range(self.nc):
                    p = ar1rand(self.tmpu[:, i], chunk_nsim)
                    self.rsims[:, i, chunk_start:chunk_end] = p

                # Force garbage collection after each chunk
                import gc
                gc.collect()

            rsims_xr = xr.DataArray(self.rsims, dims=('days', 'variables', 'nsims'), name='rsims')
            rsims_xr.to_netcdf(random_path)
            logger.info(f"Saved red noise simulations to {random_path}")

    def ci_red_noise_test(self, reduced_nsim=None, max_k_test=None, n_jobs_override=None):
        """
        Perform red noise test for classifiability index with memory optimization.

        Parameters:
        -----------
        reduced_nsim : int, optional
            Reduced number of simulations for memory efficiency (default: use self.nsim)
        max_k_test : int, optional
            Maximum k to test (default: use self.maxclust)
        n_jobs_override : int, optional
            Override number of parallel jobs (default: auto-detect)
        """
        # Use reduced parameters for memory efficiency
        test_nsim = reduced_nsim if reduced_nsim is not None else self.nsim
        test_maxclust = max_k_test if max_k_test is not None else self.maxclust

        # Auto-detect safe n_jobs based on method and available memory
        if n_jobs_override is not None:
            n_jobs = n_jobs_override
        else:
            if self.method == 'kmeans':
                n_jobs = min(8, os.cpu_count() or 1)  # Reduced from 64
            else:  # hierarchical
                n_jobs = min(4, os.cpu_count() or 1)  # Reduced from 8

        logger.info(f"Performing red noise test for CI using {self.method}")
        logger.info(f"  Test parameters: nsim={test_nsim}, maxclust={test_maxclust}, n_jobs={n_jobs}")

        if test_nsim < self.nsim:
            logger.warning(f"Using reduced simulations ({test_nsim} vs {self.nsim}) for memory efficiency")

        self.CIcis = np.zeros((test_maxclust, test_nsim))
        CIci = np.zeros((1, test_nsim))
        self.citop = np.zeros((test_maxclust, 1))
        self.cibot = np.zeros((test_maxclust, 1))

        # Choose clustering function based on method
        if self.method == 'kmeans':
            clustering_func = kmeans_ci
        else:  # hierarchical
            clustering_func = hierarchical_clustering_simple  # Use simple version without CI

        for i in range(1, test_maxclust + 1):
            ci_file = os.path.join(self.outdir, f'CIci_{i}_{self.method}_nsim{test_nsim}.nc')
            if not os.path.exists(ci_file):
                logger.info(f"Computing CI for k={i} with {test_nsim} simulations")

                # Process simulations in smaller batches to avoid memory issues
                batch_size = min(10, test_nsim)  # Process 10 simulations at a time

                for batch_start in range(0, test_nsim, batch_size):
                    batch_end = min(batch_start + batch_size, test_nsim)
                    logger.info(f"  Processing simulations {batch_start+1}-{batch_end} of {test_nsim}")

                    for j in range(batch_start, batch_end):
                        try:
                            sim = np.squeeze(self.rsims[:, :, j])
                            _, CIci[0, j] = clustering_func(sim, None, None, None, i, 100, n_jobs)
                        except Exception as e:
                            logger.warning(f"    Simulation {j} failed: {e}")
                            CIci[0, j] = np.nan

                    # Force garbage collection after each batch
                    import gc
                    gc.collect()

                cici_xr = xr.DataArray(CIci, dims=('CI', 'nsim'), name='CIci')
                cici_xr.to_netcdf(ci_file)
                logger.info(f"Saved CI confidence intervals for k={i} ({self.method}) to {ci_file}")

            tmp = xr.open_dataset(ci_file)
            self.CIcis[i - 1, :] = tmp.CIci

            # Handle NaN values in CI calculations
            valid_cis = self.CIcis[i - 1, :][~np.isnan(self.CIcis[i - 1, :])]
            if len(valid_cis) > 0:
                cisort = np.sort(valid_cis)
                self.citop[i - 1, 0] = cisort[int(.90 * len(valid_cis))]
                self.cibot[i - 1, 0] = cisort[0]
            else:
                logger.warning(f"No valid CI values for k={i}")
                self.citop[i - 1, 0] = np.nan
                self.cibot[i - 1, 0] = np.nan

    def plot_ci_ci(self):
        """
        Plot the classifiability index with confidence intervals.
        """
        x = np.arange(1, self.maxclust + 1)
        fig, ax = plt.subplots()
        ax.plot(self.CI)
        ax.set_ylabel('CI')
        ax.set_xlabel('Cluster')
        ax.set_title(f'Classifiability Index ({self.method.title()} Clustering)')
        ax.set_xlim(1, self.maxclust)
        for a in range(self.maxclust):
            ax.plot((a + 1, a + 1), (self.citop[a, 0], self.cibot[a, 0]), 'red')
        output_path = os.path.join(self.outdir, f'plot_CIci_{self.method}.png')
        plt.savefig(output_path)
        plt.close(fig)
        logger.info(f"Saved CI with confidence intervals plot to {output_path}")

        fig, ax = plt.subplots()
        ax.plot(self.CI)
        ax.set_ylabel('CI')
        ax.set_xlabel('Cluster')
        ax.set_title(f'Classifiability Index ({self.method.title()} Clustering)')
        ax.set_xlim(1, self.maxclust)
        for a in range(self.maxclust):
            ax.plot((a + 1, a + 1), (self.citop[a, 0], self.cibot[a, 0]), 'red')
        ax.fill_between(x, np.squeeze(self.citop), np.squeeze(self.cibot), color='silver')
        output_path = os.path.join(self.outdir, f'plot_CIci_shaded_{self.method}.png')
        plt.savefig(output_path)
        plt.close(fig)
        logger.info(f"Saved CI with shaded confidence intervals plot to {output_path}")

    def run_memory_efficient(self, reduced_nsim=20, max_k_test=15, chunk_size=10):
        """
        Run the complete red noise analysis with memory-efficient parameters.

        Parameters:
        -----------
        reduced_nsim : int, optional
            Reduced number of simulations (default: 20)
        max_k_test : int, optional
            Maximum k to test (default: 15)
        chunk_size : int, optional
            Chunk size for processing (default: 10)
        """
        logger.info("Running memory-efficient red noise analysis")
        logger.info(f"Parameters: nsim={reduced_nsim}, max_k={max_k_test}, chunk_size={chunk_size}")

        # Temporarily reduce nsim for memory efficiency
        original_nsim = self.nsim
        original_maxclust = self.maxclust

        try:
            # Use reduced parameters
            self.nsim = reduced_nsim
            self.maxclust = min(max_k_test, self.maxclust)

            # Generate red noise with chunking
            self.generate_red_noise(chunk_size=chunk_size)

            # Run CI test with reduced parameters
            self.ci_red_noise_test(
                reduced_nsim=reduced_nsim,
                max_k_test=max_k_test,
                n_jobs_override=2  # Conservative n_jobs
            )

            # Create plots
            self.plot_ci_ci()

            logger.info("Memory-efficient analysis completed successfully")

        finally:
            # Restore original parameters
            self.nsim = original_nsim
            self.maxclust = original_maxclust
